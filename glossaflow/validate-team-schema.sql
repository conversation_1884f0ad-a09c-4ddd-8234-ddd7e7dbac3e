-- Team Management Schema Validation Script
-- This script validates the team management database schema and data integrity

\echo '🔍 Validating Team Management Database Schema...'
\echo ''

-- Check if all required tables exist
\echo '1. Checking table existence:'
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'team_members') 
    THEN '✅ team_members table exists'
    ELSE '❌ team_members table missing'
  END as team_members_check;

SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'team_roles') 
    THEN '✅ team_roles table exists'
    ELSE '❌ team_roles table missing'
  E<PERSON> as team_roles_check;

SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'team_invitations') 
    THEN '✅ team_invitations table exists'
    ELSE '❌ team_invitations table missing'
  END as team_invitations_check;

SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'team_member_stats') 
    THEN '✅ team_member_stats table exists'
    ELSE '❌ team_member_stats table missing'
  <PERSON><PERSON> as team_member_stats_check;

\echo ''
\echo '2. Checking table structures:'

-- Check team_members table structure
\echo 'team_members table columns:'
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'team_members' 
ORDER BY ordinal_position;

\echo ''
\echo 'team_roles table columns:'
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'team_roles' 
ORDER BY ordinal_position;

\echo ''
\echo 'team_invitations table columns:'
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'team_invitations' 
ORDER BY ordinal_position;

\echo ''
\echo '3. Checking Row Level Security policies:'
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE tablename IN ('team_members', 'team_roles', 'team_invitations', 'team_member_stats')
ORDER BY tablename, policyname;

\echo ''
\echo '4. Checking sample data:'
SELECT 'team_roles' as table_name, COUNT(*) as row_count FROM team_roles
UNION ALL
SELECT 'team_members' as table_name, COUNT(*) as row_count FROM team_members
UNION ALL
SELECT 'team_invitations' as table_name, COUNT(*) as row_count FROM team_invitations
UNION ALL
SELECT 'team_member_stats' as table_name, COUNT(*) as row_count FROM team_member_stats;

\echo ''
\echo '5. Sample team roles:'
SELECT id, name, description, is_system_role 
FROM team_roles 
ORDER BY name;

\echo ''
\echo '6. Sample team members (if any):'
SELECT id, role_name, status, languages, specializations, timezone
FROM team_members 
LIMIT 5;

\echo ''
\echo '✅ Team Management Schema Validation Complete!'
