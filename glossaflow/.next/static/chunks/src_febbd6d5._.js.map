{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/accessibility/SkipLink.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface SkipLinkProps {\n  href: string;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function SkipLink({ href, children, className = '' }: SkipLinkProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  return (\n    <a\n      href={href}\n      className={`\n        fixed top-4 left-4 z-50 px-4 py-2 bg-blue-600 text-white rounded-md\n        transform transition-transform duration-200 ease-in-out\n        focus:translate-y-0 focus:opacity-100\n        ${isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'}\n        ${className}\n      `}\n      onFocus={() => setIsVisible(true)}\n      onBlur={() => setIsVisible(false)}\n    >\n      {children}\n    </a>\n  );\n}\n\nexport function SkipLinks() {\n  return (\n    <>\n      <SkipLink href=\"#main-content\">Skip to main content</SkipLink>\n      <SkipLink href=\"#navigation\">Skip to navigation</SkipLink>\n      <SkipLink href=\"#search\">Skip to search</SkipLink>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAUO,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAiB;;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAC;;;;QAIV,EAAE,YAAY,8BAA8B,8BAA8B;QAC1E,EAAE,UAAU;MACd,CAAC;QACD,SAAS,IAAM,aAAa;QAC5B,QAAQ,IAAM,aAAa;kBAE1B;;;;;;AAGP;GAnBgB;KAAA;AAqBT,SAAS;IACd,qBACE;;0BACE,6LAAC;gBAAS,MAAK;0BAAgB;;;;;;0BAC/B,6LAAC;gBAAS,MAAK;0BAAc;;;;;;0BAC7B,6LAAC;gBAAS,MAAK;0BAAU;;;;;;;;AAG/B;MARgB", "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/accessibility/ScreenReaderOnly.tsx"], "sourcesContent": ["interface ScreenReaderOnlyProps {\n  children: React.ReactNode;\n  as?: keyof JSX.IntrinsicElements;\n  className?: string;\n}\n\nexport function ScreenReaderOnly({ \n  children, \n  as: Component = 'span',\n  className = '' \n}: ScreenReaderOnlyProps) {\n  return (\n    <Component \n      className={`sr-only ${className}`}\n      aria-hidden=\"false\"\n    >\n      {children}\n    </Component>\n  );\n}\n\n// Utility component for live regions\ninterface LiveRegionProps {\n  children: React.ReactNode;\n  priority?: 'polite' | 'assertive';\n  atomic?: boolean;\n  relevant?: 'additions' | 'removals' | 'text' | 'all';\n}\n\nexport function LiveRegion({ \n  children, \n  priority = 'polite',\n  atomic = true,\n  relevant = 'all'\n}: LiveRegionProps) {\n  return (\n    <div\n      aria-live={priority}\n      aria-atomic={atomic}\n      aria-relevant={relevant}\n      className=\"sr-only\"\n    >\n      {children}\n    </div>\n  );\n}\n\n// Component for status messages\ninterface StatusMessageProps {\n  message: string;\n  type?: 'success' | 'error' | 'warning' | 'info';\n  visible?: boolean;\n}\n\nexport function StatusMessage({ \n  message, \n  type = 'info',\n  visible = true \n}: StatusMessageProps) {\n  if (!visible) return null;\n\n  const priority = type === 'error' ? 'assertive' : 'polite';\n\n  return (\n    <LiveRegion priority={priority}>\n      <span role=\"status\" aria-label={`${type}: ${message}`}>\n        {message}\n      </span>\n    </LiveRegion>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAMO,SAAS,iBAAiB,EAC/B,QAAQ,EACR,IAAI,YAAY,MAAM,EACtB,YAAY,EAAE,EACQ;IACtB,qBACE,6LAAC;QACC,WAAW,CAAC,QAAQ,EAAE,WAAW;QACjC,eAAY;kBAEX;;;;;;AAGP;KAbgB;AAuBT,SAAS,WAAW,EACzB,QAAQ,EACR,WAAW,QAAQ,EACnB,SAAS,IAAI,EACb,WAAW,KAAK,EACA;IAChB,qBACE,6LAAC;QACC,aAAW;QACX,eAAa;QACb,iBAAe;QACf,WAAU;kBAET;;;;;;AAGP;MAhBgB;AAyBT,SAAS,cAAc,EAC5B,OAAO,EACP,OAAO,MAAM,EACb,UAAU,IAAI,EACK;IACnB,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,WAAW,SAAS,UAAU,cAAc;IAElD,qBACE,6LAAC;QAAW,UAAU;kBACpB,cAAA,6LAAC;YAAK,MAAK;YAAS,cAAY,GAAG,KAAK,EAAE,EAAE,SAAS;sBAClD;;;;;;;;;;;AAIT;MAhBgB", "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useSession, signOut } from 'next-auth/react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';\nimport { SkipLinks } from '@/components/accessibility/SkipLink';\nimport { ScreenReaderOnly } from '@/components/accessibility/ScreenReaderOnly';\nimport {\n  Home,\n  FolderOpen,\n  BookOpen,\n  Users,\n  Settings,\n  LogOut,\n  Menu,\n  Bell,\n  Search,\n  Plus,\n} from 'lucide-react';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Projects', href: '/dashboard/projects', icon: FolderOpen },\n  { name: 'Terminology', href: '/dashboard/terminology', icon: BookOpen },\n  { name: 'Team', href: '/dashboard/team', icon: Users },\n  { name: 'Settings', href: '/dashboard/settings', icon: Settings },\n];\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const { data: session } = useSession();\n  const pathname = usePathname();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <SkipLinks />\n\n      {/* Mobile sidebar */}\n      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>\n        <SheetContent side=\"left\" className=\"w-64 p-0\">\n          <div className=\"flex h-full flex-col\">\n            <div className=\"flex h-16 items-center px-6 border-b\">\n              <Link href=\"/dashboard\" className=\"flex items-center\">\n                <span className=\"text-xl font-bold text-blue-600\">GlossaFlow</span>\n                <ScreenReaderOnly>- Translation Management Platform</ScreenReaderOnly>\n              </Link>\n            </div>\n            <nav\n              className=\"flex-1 space-y-1 px-3 py-4\"\n              id=\"navigation\"\n              role=\"navigation\"\n              aria-label=\"Main navigation\"\n            >\n              {navigation.map((item) => {\n                const isActive = pathname === item.href;\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-blue-100 text-blue-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                    aria-current={isActive ? 'page' : undefined}\n                  >\n                    <item.icon className=\"mr-3 h-5 w-5\" aria-hidden=\"true\" />\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n        </SheetContent>\n      </Sheet>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 items-center px-6 border-b\">\n            <Link href=\"/dashboard\" className=\"flex items-center\">\n              <span className=\"text-xl font-bold text-blue-600\">GlossaFlow</span>\n            </Link>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-3 py-4\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                    isActive\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-700 hover:bg-gray-100'\n                  }`}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top navigation */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1 items-center\">\n              <Search className=\"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 pl-3\" />\n              <input\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm\"\n                placeholder=\"Search projects, terminology...\"\n                type=\"search\"\n              />\n            </div>\n            <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n              <Button size=\"sm\" className=\"hidden sm:flex\">\n                <Plus className=\"mr-2 h-4 w-4\" />\n                New Project\n              </Button>\n\n              <Button variant=\"ghost\" size=\"sm\">\n                <Bell className=\"h-5 w-5\" />\n              </Button>\n\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarImage src={session?.user?.image || ''} alt={session?.user?.name || ''} />\n                      <AvatarFallback>\n                        {session?.user?.name?.charAt(0) || session?.user?.email?.charAt(0) || 'U'}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <DropdownMenuLabel className=\"font-normal\">\n                    <div className=\"flex flex-col space-y-1\">\n                      <p className=\"text-sm font-medium leading-none\">{session?.user?.name}</p>\n                      <p className=\"text-xs leading-none text-muted-foreground\">\n                        {session?.user?.email}\n                      </p>\n                    </div>\n                  </DropdownMenuLabel>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/profile\">Profile</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/settings\">Settings</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem\n                    className=\"text-red-600\"\n                    onClick={() => signOut({ callbackUrl: '/auth/signin' })}\n                  >\n                    <LogOut className=\"mr-2 h-4 w-4\" />\n                    Sign out\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main\n          className=\"py-6\"\n          id=\"main-content\"\n          role=\"main\"\n          aria-label=\"Main content\"\n        >\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAnBA;;;;;;;;;;;;AAoCA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,sMAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,qNAAA,CAAA,aAAU;IAAC;IAClE;QAAE,MAAM;QAAe,MAAM;QAA0B,MAAM,iNAAA,CAAA,WAAQ;IAAC;IACtE;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,uMAAA,CAAA,QAAK;IAAC;IACrD;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,6MAAA,CAAA,WAAQ;IAAC;CACjE;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IAChE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,kJAAA,CAAA,YAAS;;;;;0BAGV,6LAAC,oIAAA,CAAA,QAAK;gBAAC,MAAM;gBAAa,cAAc;0BACtC,cAAA,6LAAC,oIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAO,WAAU;8BAClC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;;sDAChC,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;sDAClD,6LAAC,0JAAA,CAAA,mBAAgB;sDAAC;;;;;;;;;;;;;;;;;0CAGtB,6LAAC;gCACC,WAAU;gCACV,IAAG;gCACH,MAAK;gCACL,cAAW;0CAEV,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,mCACJ;wCACF,SAAS,IAAM,eAAe;wCAC9B,gBAAc,WAAW,SAAS;;0DAElC,6LAAC,KAAK,IAAI;gDAAC,WAAU;gDAAe,eAAY;;;;;;4CAC/C,KAAK,IAAI;;uCAXL,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;;;;;;;;;;;;0BAOR,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAChC,cAAA,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;sCAGtD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,mCACJ;;sDAEF,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCATL,KAAK,IAAI;;;;;4BAYpB;;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDACC,WAAU;gDACV,aAAY;gDACZ,MAAK;;;;;;;;;;;;kDAGT,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;;kEAC1B,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAInC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAGlB,6LAAC,+IAAA,CAAA,eAAY;;kEACX,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,WAAU;sEAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,6LAAC,qIAAA,CAAA,cAAW;wEAAC,KAAK,SAAS,MAAM,SAAS;wEAAI,KAAK,SAAS,MAAM,QAAQ;;;;;;kFAC1E,6LAAC,qIAAA,CAAA,iBAAc;kFACZ,SAAS,MAAM,MAAM,OAAO,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;kEAK9E,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,WAAU;wDAAO,OAAM;wDAAM,UAAU;;0EAC1D,6LAAC,+IAAA,CAAA,oBAAiB;gEAAC,WAAU;0EAC3B,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAoC,SAAS,MAAM;;;;;;sFAChE,6LAAC;4EAAE,WAAU;sFACV,SAAS,MAAM;;;;;;;;;;;;;;;;;0EAItB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0EACtB,6LAAC,+IAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;8EAAqB;;;;;;;;;;;0EAElC,6LAAC,+IAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;8EAAsB;;;;;;;;;;;0EAEnC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0EACtB,6LAAC,+IAAA,CAAA,mBAAgB;gEACf,WAAU;gEACV,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;wEAAE,aAAa;oEAAe;;kFAErD,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/C,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,MAAK;wBACL,cAAW;kCAEX,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAtKgB;;QACY,iJAAA,CAAA,aAAU;QACnB,qIAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1441, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1473, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1525, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/api/team.ts"], "sourcesContent": ["import { baseApi, ApiResponse, PaginatedResponse } from './base';\n\nexport interface TeamRole {\n  id: string;\n  name: string;\n  description?: string;\n  permissions: Record<string, any>;\n  isSystemRole: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface TeamMember {\n  id: string;\n  userId: string;\n  organizationId: string;\n  roleId: string;\n  roleName: string;\n  status: 'active' | 'inactive' | 'pending' | 'suspended';\n  languages: string[];\n  specializations: string[];\n  bio?: string;\n  hourlyRate?: number;\n  timezone: string;\n  joinedAt: string;\n  lastActiveAt?: string;\n  createdAt: string;\n  updatedAt: string;\n  createdBy?: string;\n  // User information (joined from auth.users)\n  user?: {\n    id: string;\n    name?: string;\n    email: string;\n    avatar?: string;\n  };\n  // Statistics (joined from team_member_stats)\n  stats?: {\n    projectsCompleted: number;\n    wordsTranslated: number;\n    segmentsTranslated: number;\n    segmentsReviewed: number;\n    averageRating: number;\n    currentProjects: number;\n    totalHoursWorked: number;\n  };\n}\n\nexport interface TeamInvitation {\n  id: string;\n  email: string;\n  organizationId: string;\n  roleId: string;\n  roleName: string;\n  invitedBy: string;\n  status: 'pending' | 'accepted' | 'declined' | 'expired';\n  token: string;\n  expiresAt: string;\n  acceptedAt?: string;\n  createdAt: string;\n  updatedAt: string;\n  // Invited by user info\n  invitedByUser?: {\n    id: string;\n    name?: string;\n    email: string;\n  };\n}\n\nexport interface CreateTeamMemberRequest {\n  userId: string;\n  roleId: string;\n  languages?: string[];\n  specializations?: string[];\n  bio?: string;\n  hourlyRate?: number;\n  timezone?: string;\n}\n\nexport interface UpdateTeamMemberRequest extends Partial<CreateTeamMemberRequest> {\n  status?: TeamMember['status'];\n}\n\nexport interface CreateInvitationRequest {\n  email: string;\n  roleId: string;\n  message?: string;\n}\n\nexport interface TeamFilters {\n  search?: string;\n  role?: string;\n  status?: TeamMember['status'];\n  language?: string;\n  specialization?: string;\n  page?: number;\n  limit?: number;\n}\n\nexport interface TeamStats {\n  totalMembers: number;\n  activeMembers: number;\n  inactiveMembers: number;\n  pendingInvitations: number;\n  byRole: Record<string, number>;\n  byLanguage: Record<string, number>;\n  bySpecialization: Record<string, number>;\n}\n\nexport const teamApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getTeamMembers: builder.query<PaginatedResponse<TeamMember>, TeamFilters>({\n      query: (filters = {}) => {\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach(([key, value]) => {\n          if (value !== undefined && value !== null && value !== '') {\n            params.append(key, value.toString());\n          }\n        });\n        return {\n          url: `team/members?${params.toString()}`,\n          method: 'GET',\n        };\n      },\n      providesTags: (result) =>\n        result?.data.items\n          ? [\n              ...result.data.items.map(({ id }) => ({ type: 'TeamMember' as const, id })),\n              { type: 'TeamMember', id: 'LIST' },\n            ]\n          : [{ type: 'TeamMember', id: 'LIST' }],\n    }),\n\n    getTeamMember: builder.query<ApiResponse<TeamMember>, string>({\n      query: (id) => ({\n        url: `team/members/${id}`,\n        method: 'GET',\n      }),\n      providesTags: (result, error, id) => [{ type: 'TeamMember', id }],\n    }),\n\n    getTeamStats: builder.query<ApiResponse<TeamStats>, void>({\n      query: () => ({\n        url: 'team/stats',\n        method: 'GET',\n      }),\n      providesTags: [{ type: 'TeamMember', id: 'STATS' }],\n    }),\n\n    getTeamRoles: builder.query<ApiResponse<TeamRole[]>, void>({\n      query: () => ({\n        url: 'team/roles',\n        method: 'GET',\n      }),\n      providesTags: [{ type: 'TeamRole', id: 'LIST' }],\n    }),\n\n    createTeamMember: builder.mutation<ApiResponse<TeamMember>, CreateTeamMemberRequest>({\n      query: (data) => ({\n        url: 'team/members',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: [\n        { type: 'TeamMember', id: 'LIST' },\n        { type: 'TeamMember', id: 'STATS' },\n      ],\n    }),\n\n    updateTeamMember: builder.mutation<ApiResponse<TeamMember>, {\n      id: string;\n      data: UpdateTeamMemberRequest;\n    }>({\n      query: ({ id, data }) => ({\n        url: `team/members/${id}`,\n        method: 'PATCH',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [\n        { type: 'TeamMember', id },\n        { type: 'TeamMember', id: 'LIST' },\n        { type: 'TeamMember', id: 'STATS' },\n      ],\n    }),\n\n    deleteTeamMember: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `team/members/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (result, error, id) => [\n        { type: 'TeamMember', id },\n        { type: 'TeamMember', id: 'LIST' },\n        { type: 'TeamMember', id: 'STATS' },\n      ],\n    }),\n\n    getTeamInvitations: builder.query<PaginatedResponse<TeamInvitation>, {\n      status?: TeamInvitation['status'];\n      page?: number;\n      limit?: number;\n    }>({\n      query: (filters = {}) => {\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach(([key, value]) => {\n          if (value !== undefined && value !== null && value !== '') {\n            params.append(key, value.toString());\n          }\n        });\n        return {\n          url: `team/invitations?${params.toString()}`,\n          method: 'GET',\n        };\n      },\n      providesTags: [{ type: 'TeamInvitation', id: 'LIST' }],\n    }),\n\n    createInvitation: builder.mutation<ApiResponse<TeamInvitation>, CreateInvitationRequest>({\n      query: (data) => ({\n        url: 'team/invitations',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: [\n        { type: 'TeamInvitation', id: 'LIST' },\n        { type: 'TeamMember', id: 'STATS' },\n      ],\n    }),\n\n    resendInvitation: builder.mutation<ApiResponse<TeamInvitation>, string>({\n      query: (id) => ({\n        url: `team/invitations/${id}/resend`,\n        method: 'POST',\n      }),\n      invalidatesTags: [{ type: 'TeamInvitation', id: 'LIST' }],\n    }),\n\n    cancelInvitation: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `team/invitations/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: [\n        { type: 'TeamInvitation', id: 'LIST' },\n        { type: 'TeamMember', id: 'STATS' },\n      ],\n    }),\n\n    acceptInvitation: builder.mutation<ApiResponse<TeamMember>, {\n      token: string;\n      userData?: {\n        name: string;\n        password: string;\n      };\n    }>({\n      query: (data) => ({\n        url: 'team/invitations/accept',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: [\n        { type: 'TeamInvitation', id: 'LIST' },\n        { type: 'TeamMember', id: 'LIST' },\n        { type: 'TeamMember', id: 'STATS' },\n      ],\n    }),\n  }),\n});\n\nexport const {\n  useGetTeamMembersQuery,\n  useGetTeamMemberQuery,\n  useGetTeamStatsQuery,\n  useGetTeamRolesQuery,\n  useCreateTeamMemberMutation,\n  useUpdateTeamMemberMutation,\n  useDeleteTeamMemberMutation,\n  useGetTeamInvitationsQuery,\n  useCreateInvitationMutation,\n  useResendInvitationMutation,\n  useCancelInvitationMutation,\n  useAcceptInvitationMutation,\n} = teamApi;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;AA6GO,MAAM,UAAU,4HAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAC7C,WAAW,CAAC,UAAY,CAAC;YACvB,gBAAgB,QAAQ,KAAK,CAA6C;gBACxE,OAAO,CAAC,UAAU,CAAC,CAAC;oBAClB,MAAM,SAAS,IAAI;oBACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;wBAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;4BACzD,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;wBACnC;oBACF;oBACA,OAAO;wBACL,KAAK,CAAC,aAAa,EAAE,OAAO,QAAQ,IAAI;wBACxC,QAAQ;oBACV;gBACF;gBACA,cAAc,CAAC,SACb,QAAQ,KAAK,QACT;2BACK,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAK,CAAC;gCAAE,MAAM;gCAAuB;4BAAG,CAAC;wBACzE;4BAAE,MAAM;4BAAc,IAAI;wBAAO;qBAClC,GACD;wBAAC;4BAAE,MAAM;4BAAc,IAAI;wBAAO;qBAAE;YAC5C;YAEA,eAAe,QAAQ,KAAK,CAAkC;gBAC5D,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,aAAa,EAAE,IAAI;wBACzB,QAAQ;oBACV,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,KAAO;wBAAC;4BAAE,MAAM;4BAAc;wBAAG;qBAAE;YACnE;YAEA,cAAc,QAAQ,KAAK,CAA+B;gBACxD,OAAO,IAAM,CAAC;wBACZ,KAAK;wBACL,QAAQ;oBACV,CAAC;gBACD,cAAc;oBAAC;wBAAE,MAAM;wBAAc,IAAI;oBAAQ;iBAAE;YACrD;YAEA,cAAc,QAAQ,KAAK,CAAgC;gBACzD,OAAO,IAAM,CAAC;wBACZ,KAAK;wBACL,QAAQ;oBACV,CAAC;gBACD,cAAc;oBAAC;wBAAE,MAAM;wBAAY,IAAI;oBAAO;iBAAE;YAClD;YAEA,kBAAkB,QAAQ,QAAQ,CAAmD;gBACnF,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBACf;wBAAE,MAAM;wBAAc,IAAI;oBAAO;oBACjC;wBAAE,MAAM;wBAAc,IAAI;oBAAQ;iBACnC;YACH;YAEA,kBAAkB,QAAQ,QAAQ,CAG/B;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,aAAa,EAAE,IAAI;wBACzB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAc;wBAAG;wBACzB;4BAAE,MAAM;4BAAc,IAAI;wBAAO;wBACjC;4BAAE,MAAM;4BAAc,IAAI;wBAAQ;qBACnC;YACH;YAEA,kBAAkB,QAAQ,QAAQ,CAA4B;gBAC5D,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,aAAa,EAAE,IAAI;wBACzB,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,KAAO;wBACtC;4BAAE,MAAM;4BAAc;wBAAG;wBACzB;4BAAE,MAAM;4BAAc,IAAI;wBAAO;wBACjC;4BAAE,MAAM;4BAAc,IAAI;wBAAQ;qBACnC;YACH;YAEA,oBAAoB,QAAQ,KAAK,CAI9B;gBACD,OAAO,CAAC,UAAU,CAAC,CAAC;oBAClB,MAAM,SAAS,IAAI;oBACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;wBAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;4BACzD,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;wBACnC;oBACF;oBACA,OAAO;wBACL,KAAK,CAAC,iBAAiB,EAAE,OAAO,QAAQ,IAAI;wBAC5C,QAAQ;oBACV;gBACF;gBACA,cAAc;oBAAC;wBAAE,MAAM;wBAAkB,IAAI;oBAAO;iBAAE;YACxD;YAEA,kBAAkB,QAAQ,QAAQ,CAAuD;gBACvF,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBACf;wBAAE,MAAM;wBAAkB,IAAI;oBAAO;oBACrC;wBAAE,MAAM;wBAAc,IAAI;oBAAQ;iBACnC;YACH;YAEA,kBAAkB,QAAQ,QAAQ,CAAsC;gBACtE,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,iBAAiB,EAAE,GAAG,OAAO,CAAC;wBACpC,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;wBAAE,MAAM;wBAAkB,IAAI;oBAAO;iBAAE;YAC3D;YAEA,kBAAkB,QAAQ,QAAQ,CAA4B;gBAC5D,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,iBAAiB,EAAE,IAAI;wBAC7B,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBACf;wBAAE,MAAM;wBAAkB,IAAI;oBAAO;oBACrC;wBAAE,MAAM;wBAAc,IAAI;oBAAQ;iBACnC;YACH;YAEA,kBAAkB,QAAQ,QAAQ,CAM/B;gBACD,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBACf;wBAAE,MAAM;wBAAkB,IAAI;oBAAO;oBACrC;wBAAE,MAAM;wBAAc,IAAI;oBAAO;oBACjC;wBAAE,MAAM;wBAAc,IAAI;oBAAQ;iBACnC;YACH;QACF,CAAC;AACH;AAEO,MAAM,EACX,sBAAsB,EACtB,qBAAqB,EACrB,oBAAoB,EACpB,oBAAoB,EACpB,2BAA2B,EAC3B,2BAA2B,EAC3B,2BAA2B,EAC3B,0BAA0B,EAC1B,2BAA2B,EAC3B,2BAA2B,EAC3B,2BAA2B,EAC3B,2BAA2B,EAC5B,GAAG", "debugId": null}}, {"offset": {"line": 1765, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/app/dashboard/team/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { DashboardLayout } from '@/components/layout/DashboardLayout';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { DeleteConfirmDialog } from '@/components/common/DeleteConfirmDialog';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport {\n  Plus,\n  Search,\n  Filter,\n  MoreHorizontal,\n  Mail,\n  Phone,\n  Globe,\n  Calendar,\n  BarChart3,\n  Edit,\n  Trash2,\n  UserPlus,\n  AlertCircle,\n  RefreshCw\n} from 'lucide-react';\nimport {\n  useGetTeamMembersQuery,\n  useGetTeamStatsQuery,\n  useGetTeamRolesQuery,\n  useUpdateTeamMemberMutation,\n  useDeleteTeamMemberMutation,\n  type TeamMember,\n  type TeamFilters\n} from '@/lib/api/team';\n\n// Constants for styling and options\n\nconst roleColors = {\n  'Lead Translator': 'bg-purple-100 text-purple-800',\n  'Senior Reviewer': 'bg-blue-100 text-blue-800',\n  'Project Manager': 'bg-green-100 text-green-800',\n  'Translator': 'bg-gray-100 text-gray-800',\n  'Reviewer': 'bg-yellow-100 text-yellow-800',\n};\n\nconst statusColors = {\n  active: 'bg-green-100 text-green-800',\n  inactive: 'bg-gray-100 text-gray-800',\n  pending: 'bg-yellow-100 text-yellow-800',\n  suspended: 'bg-red-100 text-red-800',\n};\n\nconst statuses = ['all', 'active', 'inactive', 'pending', 'suspended'];\n\nexport default function TeamPage() {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedRole, setSelectedRole] = useState('all');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [selectedLanguage, setSelectedLanguage] = useState('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [memberToDelete, setMemberToDelete] = useState<TeamMember | null>(null);\n\n  // Build filters for API query\n  const filters: TeamFilters = {\n    page: currentPage,\n    limit: 10,\n    ...(searchQuery && { search: searchQuery }),\n    ...(selectedRole !== 'all' && { role: selectedRole }),\n    ...(selectedStatus !== 'all' && {\n      status: selectedStatus as 'active' | 'inactive' | 'pending' | 'suspended'\n    }),\n    ...(selectedLanguage !== 'all' && { language: selectedLanguage }),\n  };\n\n  // API queries\n  const {\n    data: teamResponse,\n    isLoading: isLoadingTeam,\n    isError: isTeamError,\n    refetch: refetchTeam,\n  } = useGetTeamMembersQuery(filters, {\n    refetchOnFocus: true,\n    refetchOnMountOrArgChange: true,\n  });\n\n  const {\n    data: statsResponse,\n    isLoading: isLoadingStats,\n  } = useGetTeamStatsQuery();\n\n  const {\n    data: rolesResponse,\n  } = useGetTeamRolesQuery();\n\n  // Mutations\n  const [updateTeamMember] = useUpdateTeamMemberMutation();\n  const [deleteTeamMember, { isLoading: isDeleting }] = useDeleteTeamMemberMutation();\n\n  const teamMembers = teamResponse?.data?.items || [];\n  const totalMembers = teamResponse?.data?.total || 0;\n  const stats = statsResponse?.data || {\n    totalMembers: 0,\n    activeMembers: 0,\n    inactiveMembers: 0,\n    pendingInvitations: 0,\n    byRole: {},\n    byLanguage: {},\n    bySpecialization: {},\n  };\n  const roles = rolesResponse?.data || [];\n\n  const handleEditMember = async (member: TeamMember) => {\n    // For now, just log - could open an edit dialog\n    console.log('Edit member:', member);\n  };\n\n  const handleDeleteMember = async () => {\n    if (!memberToDelete) return;\n\n    try {\n      await deleteTeamMember(memberToDelete.id).unwrap();\n      setShowDeleteDialog(false);\n      setMemberToDelete(null);\n    } catch (error) {\n      console.error('Failed to delete team member:', error);\n    }\n  };\n\n  const openDeleteDialog = (member: TeamMember) => {\n    setMemberToDelete(member);\n    setShowDeleteDialog(true);\n  };\n\n  const handleStatusChange = async (memberId: string, newStatus: TeamMember['status']) => {\n    try {\n      await updateTeamMember({\n        id: memberId,\n        data: { status: newStatus }\n      }).unwrap();\n    } catch (error) {\n      console.error('Failed to update member status:', error);\n    }\n  };\n\n  // Get unique languages from team members for filter\n  const languages = Array.from(new Set(\n    teamMembers.flatMap(member => member.languages || [])\n  )).sort();\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Team Management</h1>\n            <p className=\"text-gray-600\">Manage your translation team members</p>\n          </div>\n          <Button>\n            <UserPlus className=\"mr-2 h-4 w-4\" />\n            Invite Member\n          </Button>\n        </div>\n\n        {/* Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <BarChart3 className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Total Members</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{mockTeamMembers.length}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-green-100 rounded-lg\">\n                  <BarChart3 className=\"h-6 w-6 text-green-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Active Members</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {mockTeamMembers.filter(m => m.status === 'active').length}\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-purple-100 rounded-lg\">\n                  <Globe className=\"h-6 w-6 text-purple-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Languages</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {[...new Set(mockTeamMembers.flatMap(m => m.languages))].length}\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-orange-100 rounded-lg\">\n                  <BarChart3 className=\"h-6 w-6 text-orange-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Active Projects</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {mockTeamMembers.reduce((sum, m) => sum + m.currentProjects, 0)}\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Filters */}\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <div className=\"relative flex-1\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <Input\n                  placeholder=\"Search team members...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n              <div className=\"flex gap-2\">\n                <Button\n                  variant={selectedRole === 'all' ? 'default' : 'outline'}\n                  size=\"sm\"\n                  onClick={() => setSelectedRole('all')}\n                >\n                  All Roles\n                </Button>\n                {roles.map(role => (\n                  <Button\n                    key={role}\n                    variant={selectedRole === role ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => setSelectedRole(role)}\n                  >\n                    {role}\n                  </Button>\n                ))}\n              </div>\n              <div className=\"flex gap-2\">\n                <Button\n                  variant={selectedStatus === 'all' ? 'default' : 'outline'}\n                  size=\"sm\"\n                  onClick={() => setSelectedStatus('all')}\n                >\n                  All Status\n                </Button>\n                {statuses.map(status => (\n                  <Button\n                    key={status}\n                    variant={selectedStatus === status ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => setSelectedStatus(status)}\n                  >\n                    {status.charAt(0).toUpperCase() + status.slice(1)}\n                  </Button>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Team Members Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n          {filteredMembers.map((member) => (\n            <Card key={member.id} className=\"hover:shadow-md transition-shadow\">\n              <CardHeader className=\"pb-3\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <Avatar className=\"h-12 w-12\">\n                      <AvatarImage src={member.avatar} />\n                      <AvatarFallback>\n                        {member.name.split(' ').map(n => n[0]).join('')}\n                      </AvatarFallback>\n                    </Avatar>\n                    <div>\n                      <CardTitle className=\"text-lg\">{member.name}</CardTitle>\n                      <p className=\"text-sm text-gray-600\">{member.email}</p>\n                    </div>\n                  </div>\n                  <DropdownMenu>\n                    <DropdownMenuTrigger asChild>\n                      <Button variant=\"ghost\" size=\"sm\">\n                        <MoreHorizontal className=\"h-4 w-4\" />\n                      </Button>\n                    </DropdownMenuTrigger>\n                    <DropdownMenuContent align=\"end\">\n                      <DropdownMenuItem>\n                        <Edit className=\"mr-2 h-4 w-4\" />\n                        Edit\n                      </DropdownMenuItem>\n                      <DropdownMenuItem>\n                        <Mail className=\"mr-2 h-4 w-4\" />\n                        Send Message\n                      </DropdownMenuItem>\n                      <DropdownMenuItem className=\"text-red-600\">\n                        <Trash2 className=\"mr-2 h-4 w-4\" />\n                        Remove\n                      </DropdownMenuItem>\n                    </DropdownMenuContent>\n                  </DropdownMenu>\n                </div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <Badge className={roleColors[member.role as keyof typeof roleColors]}>\n                      {member.role}\n                    </Badge>\n                    <Badge className={statusColors[member.status as keyof typeof statusColors]}>\n                      {member.status}\n                    </Badge>\n                  </div>\n\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600 mb-1\">Languages</p>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {member.languages.map((lang) => (\n                        <Badge key={lang} variant=\"outline\" size=\"sm\">\n                          {lang}\n                        </Badge>\n                      ))}\n                    </div>\n                  </div>\n\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600 mb-1\">Specializations</p>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {member.specializations.map((spec) => (\n                        <Badge key={spec} variant=\"outline\" size=\"sm\">\n                          {spec}\n                        </Badge>\n                      ))}\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                    <div>\n                      <p className=\"font-medium text-gray-600\">Projects</p>\n                      <p>{member.projectsCompleted} completed</p>\n                      <p>{member.currentProjects} active</p>\n                    </div>\n                    <div>\n                      <p className=\"font-medium text-gray-600\">Performance</p>\n                      <p>⭐ {member.averageRating}/5.0</p>\n                      <p>{member.wordsTranslated.toLocaleString()} words</p>\n                    </div>\n                  </div>\n\n                  <div className=\"text-xs text-gray-500\">\n                    Joined {member.joinedAt}\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {filteredMembers.length === 0 && (\n          <Card>\n            <CardContent className=\"p-12 text-center\">\n              <div className=\"text-gray-400 mb-4\">\n                <Search className=\"h-12 w-12 mx-auto\" />\n              </div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No team members found</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Try adjusting your search criteria or invite new members to join your team.\n              </p>\n              <Button>\n                <UserPlus className=\"mr-2 h-4 w-4\" />\n                Invite Team Member\n              </Button>\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;;;AAlCA;;;;;;;;;;;AA4CA,oCAAoC;AAEpC,MAAM,aAAa;IACjB,mBAAmB;IACnB,mBAAmB;IACnB,mBAAmB;IACnB,cAAc;IACd,YAAY;AACd;AAEA,MAAM,eAAe;IACnB,QAAQ;IACR,UAAU;IACV,SAAS;IACT,WAAW;AACb;AAEA,MAAM,WAAW;IAAC;IAAO;IAAU;IAAY;IAAW;CAAY;AAEvD,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAExE,8BAA8B;IAC9B,MAAM,UAAuB;QAC3B,MAAM;QACN,OAAO;QACP,GAAI,eAAe;YAAE,QAAQ;QAAY,CAAC;QAC1C,GAAI,iBAAiB,SAAS;YAAE,MAAM;QAAa,CAAC;QACpD,GAAI,mBAAmB,SAAS;YAC9B,QAAQ;QACV,CAAC;QACD,GAAI,qBAAqB,SAAS;YAAE,UAAU;QAAiB,CAAC;IAClE;IAEA,cAAc;IACd,MAAM,EACJ,MAAM,YAAY,EAClB,WAAW,aAAa,EACxB,SAAS,WAAW,EACpB,SAAS,WAAW,EACrB,GAAG,CAAA,GAAA,4HAAA,CAAA,yBAAsB,AAAD,EAAE,SAAS;QAClC,gBAAgB;QAChB,2BAA2B;IAC7B;IAEA,MAAM,EACJ,MAAM,aAAa,EACnB,WAAW,cAAc,EAC1B,GAAG,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD;IAEvB,MAAM,EACJ,MAAM,aAAa,EACpB,GAAG,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD;IAEvB,YAAY;IACZ,MAAM,CAAC,iBAAiB,GAAG,CAAA,GAAA,4HAAA,CAAA,8BAA2B,AAAD;IACrD,MAAM,CAAC,kBAAkB,EAAE,WAAW,UAAU,EAAE,CAAC,GAAG,CAAA,GAAA,4HAAA,CAAA,8BAA2B,AAAD;IAEhF,MAAM,cAAc,cAAc,MAAM,SAAS,EAAE;IACnD,MAAM,eAAe,cAAc,MAAM,SAAS;IAClD,MAAM,QAAQ,eAAe,QAAQ;QACnC,cAAc;QACd,eAAe;QACf,iBAAiB;QACjB,oBAAoB;QACpB,QAAQ,CAAC;QACT,YAAY,CAAC;QACb,kBAAkB,CAAC;IACrB;IACA,MAAM,QAAQ,eAAe,QAAQ,EAAE;IAEvC,MAAM,mBAAmB,OAAO;QAC9B,gDAAgD;QAChD,QAAQ,GAAG,CAAC,gBAAgB;IAC9B;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,iBAAiB,eAAe,EAAE,EAAE,MAAM;YAChD,oBAAoB;YACpB,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,oBAAoB;IACtB;IAEA,MAAM,qBAAqB,OAAO,UAAkB;QAClD,IAAI;YACF,MAAM,iBAAiB;gBACrB,IAAI;gBACJ,MAAM;oBAAE,QAAQ;gBAAU;YAC5B,GAAG,MAAM;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,oDAAoD;IACpD,MAAM,YAAY,MAAM,IAAI,CAAC,IAAI,IAC/B,YAAY,OAAO,CAAC,CAAA,SAAU,OAAO,SAAS,IAAI,EAAE,IACnD,IAAI;IAEP,qBACE,6LAAC,kJAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,6LAAC,qIAAA,CAAA,SAAM;;8CACL,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;8BAMzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DACV,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOpE,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DACV;2DAAI,IAAI,IAAI,gBAAgB,OAAO,CAAC,CAAA,IAAK,EAAE,SAAS;qDAAG,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOzE,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DACV,gBAAgB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,eAAe,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASzE,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,iBAAiB,QAAQ,YAAY;4CAC9C,MAAK;4CACL,SAAS,IAAM,gBAAgB;sDAChC;;;;;;wCAGA,MAAM,GAAG,CAAC,CAAA,qBACT,6LAAC,qIAAA,CAAA,SAAM;gDAEL,SAAS,iBAAiB,OAAO,YAAY;gDAC7C,MAAK;gDACL,SAAS,IAAM,gBAAgB;0DAE9B;+CALI;;;;;;;;;;;8CASX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,mBAAmB,QAAQ,YAAY;4CAChD,MAAK;4CACL,SAAS,IAAM,kBAAkB;sDAClC;;;;;;wCAGA,SAAS,GAAG,CAAC,CAAA,uBACZ,6LAAC,qIAAA,CAAA,SAAM;gDAEL,SAAS,mBAAmB,SAAS,YAAY;gDACjD,MAAK;gDACL,SAAS,IAAM,kBAAkB;0DAEhC,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;+CAL1C;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAcjB,6LAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC,mIAAA,CAAA,OAAI;4BAAiB,WAAU;;8CAC9B,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,6LAAC,qIAAA,CAAA,cAAW;gEAAC,KAAK,OAAO,MAAM;;;;;;0EAC/B,6LAAC,qIAAA,CAAA,iBAAc;0EACZ,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;kEAGhD,6LAAC;;0EACC,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAW,OAAO,IAAI;;;;;;0EAC3C,6LAAC;gEAAE,WAAU;0EAAyB,OAAO,KAAK;;;;;;;;;;;;;;;;;;0DAGtD,6LAAC,+IAAA,CAAA,eAAY;;kEACX,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAC3B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG9B,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,6LAAC,+IAAA,CAAA,mBAAgB;;kFACf,6LAAC,8MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6LAAC,+IAAA,CAAA,mBAAgB;;kFACf,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6LAAC,+IAAA,CAAA,mBAAgB;gEAAC,WAAU;;kFAC1B,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO7C,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAW,UAAU,CAAC,OAAO,IAAI,CAA4B;kEACjE,OAAO,IAAI;;;;;;kEAEd,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAW,YAAY,CAAC,OAAO,MAAM,CAA8B;kEACvE,OAAO,MAAM;;;;;;;;;;;;0DAIlB,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAI,WAAU;kEACZ,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,qBACrB,6LAAC,oIAAA,CAAA,QAAK;gEAAY,SAAQ;gEAAU,MAAK;0EACtC;+DADS;;;;;;;;;;;;;;;;0DAOlB,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAI,WAAU;kEACZ,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC,qBAC3B,6LAAC,oIAAA,CAAA,QAAK;gEAAY,SAAQ;gEAAU,MAAK;0EACtC;+DADS;;;;;;;;;;;;;;;;0DAOlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA4B;;;;;;0EACzC,6LAAC;;oEAAG,OAAO,iBAAiB;oEAAC;;;;;;;0EAC7B,6LAAC;;oEAAG,OAAO,eAAe;oEAAC;;;;;;;;;;;;;kEAE7B,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAA4B;;;;;;0EACzC,6LAAC;;oEAAE;oEAAG,OAAO,aAAa;oEAAC;;;;;;;0EAC3B,6LAAC;;oEAAG,OAAO,eAAe,CAAC,cAAc;oEAAG;;;;;;;;;;;;;;;;;;;0DAIhD,6LAAC;gDAAI,WAAU;;oDAAwB;oDAC7B,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;2BArFpB,OAAO,EAAE;;;;;;;;;;gBA6FvB,gBAAgB,MAAM,KAAK,mBAC1B,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC,qIAAA,CAAA,SAAM;;kDACL,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;GA3VwB;;QA2BlB,4HAAA,CAAA,yBAAsB;QAQtB,4HAAA,CAAA,uBAAoB;QAIpB,4HAAA,CAAA,uBAAoB;QAGG,4HAAA,CAAA,8BAA2B;QACA,4HAAA,CAAA,8BAA2B;;;KA3C3D", "debugId": null}}]}