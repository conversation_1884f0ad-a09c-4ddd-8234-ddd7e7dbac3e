{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/accessibility/SkipLink.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface SkipLinkProps {\n  href: string;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function SkipLink({ href, children, className = '' }: SkipLinkProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  return (\n    <a\n      href={href}\n      className={`\n        fixed top-4 left-4 z-50 px-4 py-2 bg-blue-600 text-white rounded-md\n        transform transition-transform duration-200 ease-in-out\n        focus:translate-y-0 focus:opacity-100\n        ${isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'}\n        ${className}\n      `}\n      onFocus={() => setIsVisible(true)}\n      onBlur={() => setIsVisible(false)}\n    >\n      {children}\n    </a>\n  );\n}\n\nexport function SkipLinks() {\n  return (\n    <>\n      <SkipLink href=\"#main-content\">Skip to main content</SkipLink>\n      <SkipLink href=\"#navigation\">Skip to navigation</SkipLink>\n      <SkipLink href=\"#search\">Skip to search</SkipLink>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAUO,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAiB;;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAC;;;;QAIV,EAAE,YAAY,8BAA8B,8BAA8B;QAC1E,EAAE,UAAU;MACd,CAAC;QACD,SAAS,IAAM,aAAa;QAC5B,QAAQ,IAAM,aAAa;kBAE1B;;;;;;AAGP;GAnBgB;KAAA;AAqBT,SAAS;IACd,qBACE;;0BACE,6LAAC;gBAAS,MAAK;0BAAgB;;;;;;0BAC/B,6LAAC;gBAAS,MAAK;0BAAc;;;;;;0BAC7B,6LAAC;gBAAS,MAAK;0BAAU;;;;;;;;AAG/B;MARgB", "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/accessibility/ScreenReaderOnly.tsx"], "sourcesContent": ["interface ScreenReaderOnlyProps {\n  children: React.ReactNode;\n  as?: keyof JSX.IntrinsicElements;\n  className?: string;\n}\n\nexport function ScreenReaderOnly({ \n  children, \n  as: Component = 'span',\n  className = '' \n}: ScreenReaderOnlyProps) {\n  return (\n    <Component \n      className={`sr-only ${className}`}\n      aria-hidden=\"false\"\n    >\n      {children}\n    </Component>\n  );\n}\n\n// Utility component for live regions\ninterface LiveRegionProps {\n  children: React.ReactNode;\n  priority?: 'polite' | 'assertive';\n  atomic?: boolean;\n  relevant?: 'additions' | 'removals' | 'text' | 'all';\n}\n\nexport function LiveRegion({ \n  children, \n  priority = 'polite',\n  atomic = true,\n  relevant = 'all'\n}: LiveRegionProps) {\n  return (\n    <div\n      aria-live={priority}\n      aria-atomic={atomic}\n      aria-relevant={relevant}\n      className=\"sr-only\"\n    >\n      {children}\n    </div>\n  );\n}\n\n// Component for status messages\ninterface StatusMessageProps {\n  message: string;\n  type?: 'success' | 'error' | 'warning' | 'info';\n  visible?: boolean;\n}\n\nexport function StatusMessage({ \n  message, \n  type = 'info',\n  visible = true \n}: StatusMessageProps) {\n  if (!visible) return null;\n\n  const priority = type === 'error' ? 'assertive' : 'polite';\n\n  return (\n    <LiveRegion priority={priority}>\n      <span role=\"status\" aria-label={`${type}: ${message}`}>\n        {message}\n      </span>\n    </LiveRegion>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAMO,SAAS,iBAAiB,EAC/B,QAAQ,EACR,IAAI,YAAY,MAAM,EACtB,YAAY,EAAE,EACQ;IACtB,qBACE,6LAAC;QACC,WAAW,CAAC,QAAQ,EAAE,WAAW;QACjC,eAAY;kBAEX;;;;;;AAGP;KAbgB;AAuBT,SAAS,WAAW,EACzB,QAAQ,EACR,WAAW,QAAQ,EACnB,SAAS,IAAI,EACb,WAAW,KAAK,EACA;IAChB,qBACE,6LAAC;QACC,aAAW;QACX,eAAa;QACb,iBAAe;QACf,WAAU;kBAET;;;;;;AAGP;MAhBgB;AAyBT,SAAS,cAAc,EAC5B,OAAO,EACP,OAAO,MAAM,EACb,UAAU,IAAI,EACK;IACnB,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,WAAW,SAAS,UAAU,cAAc;IAElD,qBACE,6LAAC;QAAW,UAAU;kBACpB,cAAA,6LAAC;YAAK,MAAK;YAAS,cAAY,GAAG,KAAK,EAAE,EAAE,SAAS;sBAClD;;;;;;;;;;;AAIT;MAhBgB", "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useSession, signOut } from 'next-auth/react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';\nimport { SkipLinks } from '@/components/accessibility/SkipLink';\nimport { ScreenReaderOnly } from '@/components/accessibility/ScreenReaderOnly';\nimport {\n  Home,\n  FolderOpen,\n  BookOpen,\n  Users,\n  Settings,\n  LogOut,\n  Menu,\n  Bell,\n  Search,\n  Plus,\n} from 'lucide-react';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Projects', href: '/dashboard/projects', icon: FolderOpen },\n  { name: 'Terminology', href: '/dashboard/terminology', icon: BookOpen },\n  { name: 'Team', href: '/dashboard/team', icon: Users },\n  { name: 'Settings', href: '/dashboard/settings', icon: Settings },\n];\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const { data: session } = useSession();\n  const pathname = usePathname();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <SkipLinks />\n\n      {/* Mobile sidebar */}\n      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>\n        <SheetContent side=\"left\" className=\"w-64 p-0\">\n          <div className=\"flex h-full flex-col\">\n            <div className=\"flex h-16 items-center px-6 border-b\">\n              <Link href=\"/dashboard\" className=\"flex items-center\">\n                <span className=\"text-xl font-bold text-blue-600\">GlossaFlow</span>\n                <ScreenReaderOnly>- Translation Management Platform</ScreenReaderOnly>\n              </Link>\n            </div>\n            <nav\n              className=\"flex-1 space-y-1 px-3 py-4\"\n              id=\"navigation\"\n              role=\"navigation\"\n              aria-label=\"Main navigation\"\n            >\n              {navigation.map((item) => {\n                const isActive = pathname === item.href;\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-blue-100 text-blue-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                    aria-current={isActive ? 'page' : undefined}\n                  >\n                    <item.icon className=\"mr-3 h-5 w-5\" aria-hidden=\"true\" />\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n        </SheetContent>\n      </Sheet>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 items-center px-6 border-b\">\n            <Link href=\"/dashboard\" className=\"flex items-center\">\n              <span className=\"text-xl font-bold text-blue-600\">GlossaFlow</span>\n            </Link>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-3 py-4\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                    isActive\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-700 hover:bg-gray-100'\n                  }`}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top navigation */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1 items-center\">\n              <Search className=\"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 pl-3\" />\n              <input\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm\"\n                placeholder=\"Search projects, terminology...\"\n                type=\"search\"\n              />\n            </div>\n            <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n              <Button size=\"sm\" className=\"hidden sm:flex\">\n                <Plus className=\"mr-2 h-4 w-4\" />\n                New Project\n              </Button>\n\n              <Button variant=\"ghost\" size=\"sm\">\n                <Bell className=\"h-5 w-5\" />\n              </Button>\n\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarImage src={session?.user?.image || ''} alt={session?.user?.name || ''} />\n                      <AvatarFallback>\n                        {session?.user?.name?.charAt(0) || session?.user?.email?.charAt(0) || 'U'}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <DropdownMenuLabel className=\"font-normal\">\n                    <div className=\"flex flex-col space-y-1\">\n                      <p className=\"text-sm font-medium leading-none\">{session?.user?.name}</p>\n                      <p className=\"text-xs leading-none text-muted-foreground\">\n                        {session?.user?.email}\n                      </p>\n                    </div>\n                  </DropdownMenuLabel>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/profile\">Profile</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/settings\">Settings</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem\n                    className=\"text-red-600\"\n                    onClick={() => signOut({ callbackUrl: '/auth/signin' })}\n                  >\n                    <LogOut className=\"mr-2 h-4 w-4\" />\n                    Sign out\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main\n          className=\"py-6\"\n          id=\"main-content\"\n          role=\"main\"\n          aria-label=\"Main content\"\n        >\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAnBA;;;;;;;;;;;;AAoCA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,sMAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,qNAAA,CAAA,aAAU;IAAC;IAClE;QAAE,MAAM;QAAe,MAAM;QAA0B,MAAM,iNAAA,CAAA,WAAQ;IAAC;IACtE;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,uMAAA,CAAA,QAAK;IAAC;IACrD;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,6MAAA,CAAA,WAAQ;IAAC;CACjE;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IAChE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,kJAAA,CAAA,YAAS;;;;;0BAGV,6LAAC,oIAAA,CAAA,QAAK;gBAAC,MAAM;gBAAa,cAAc;0BACtC,cAAA,6LAAC,oIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAO,WAAU;8BAClC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;;sDAChC,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;sDAClD,6LAAC,0JAAA,CAAA,mBAAgB;sDAAC;;;;;;;;;;;;;;;;;0CAGtB,6LAAC;gCACC,WAAU;gCACV,IAAG;gCACH,MAAK;gCACL,cAAW;0CAEV,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,mCACJ;wCACF,SAAS,IAAM,eAAe;wCAC9B,gBAAc,WAAW,SAAS;;0DAElC,6LAAC,KAAK,IAAI;gDAAC,WAAU;gDAAe,eAAY;;;;;;4CAC/C,KAAK,IAAI;;uCAXL,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;;;;;;;;;;;;0BAOR,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAChC,cAAA,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;sCAGtD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,mCACJ;;sDAEF,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCATL,KAAK,IAAI;;;;;4BAYpB;;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDACC,WAAU;gDACV,aAAY;gDACZ,MAAK;;;;;;;;;;;;kDAGT,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;;kEAC1B,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAInC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAGlB,6LAAC,+IAAA,CAAA,eAAY;;kEACX,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,WAAU;sEAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,6LAAC,qIAAA,CAAA,cAAW;wEAAC,KAAK,SAAS,MAAM,SAAS;wEAAI,KAAK,SAAS,MAAM,QAAQ;;;;;;kFAC1E,6LAAC,qIAAA,CAAA,iBAAc;kFACZ,SAAS,MAAM,MAAM,OAAO,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;kEAK9E,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,WAAU;wDAAO,OAAM;wDAAM,UAAU;;0EAC1D,6LAAC,+IAAA,CAAA,oBAAiB;gEAAC,WAAU;0EAC3B,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAoC,SAAS,MAAM;;;;;;sFAChE,6LAAC;4EAAE,WAAU;sFACV,SAAS,MAAM;;;;;;;;;;;;;;;;;0EAItB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0EACtB,6LAAC,+IAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;8EAAqB;;;;;;;;;;;0EAElC,6LAAC,+IAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;8EAAsB;;;;;;;;;;;0EAEnC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0EACtB,6LAAC,+IAAA,CAAA,mBAAgB;gEACf,WAAU;gEACV,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;wEAAE,aAAa;oEAAe;;kFAErD,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/C,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,MAAK;wBACL,cAAW;kCAEX,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAtKgB;;QACY,iJAAA,CAAA,aAAU;QACnB,qIAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1441, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 1538, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/api/dashboard.ts"], "sourcesContent": ["import { baseApi } from './base';\n\n// TypeScript interfaces for dashboard data\nexport interface DashboardStats {\n  projects: {\n    total: number;\n    active: number;\n    completed: number;\n    pending: number;\n    recent: number;\n  };\n  terminology: {\n    total: number;\n    approved: number;\n    pending: number;\n    rejected: number;\n    recent: number;\n  };\n  team: {\n    total: number;\n    active: number;\n    pending: number;\n    invitations: number;\n  };\n  overview: {\n    totalProjects: number;\n    activeProjects: number;\n    totalTerms: number;\n    teamMembers: number;\n    pendingReviews: number;\n    recentActivity: number;\n  };\n}\n\nexport interface DashboardActivity {\n  id: string;\n  type: 'project' | 'terminology' | 'team' | 'invitation';\n  action: string;\n  title: string;\n  description: string;\n  user: {\n    id?: string;\n    name: string;\n    email: string;\n    avatar: string | null;\n  };\n  timestamp: string;\n  metadata: Record<string, any>;\n}\n\nexport interface DashboardActivityResponse {\n  activities: DashboardActivity[];\n  total: number;\n}\n\nexport interface RecentProject {\n  id: string;\n  name: string;\n  description: string | null;\n  status: string;\n  progress: number;\n  sourceLanguage: string;\n  targetLanguage: string;\n  targetLanguages: string[] | string;\n  deadline: string | null;\n  createdAt: string;\n  updatedAt: string;\n  createdBy: string;\n}\n\nexport interface RecentProjectsResponse {\n  projects: RecentProject[];\n  total: number;\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data: T;\n  message?: string;\n  error?: string;\n}\n\n// Dashboard API slice\nexport const dashboardApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getDashboardStats: builder.query<ApiResponse<DashboardStats>, void>({\n      query: () => '/dashboard/stats',\n      providesTags: ['Project', 'Terminology', 'TeamMember'],\n      // Refetch every 5 minutes\n      keepUnusedDataFor: 300,\n    }),\n\n    getDashboardActivity: builder.query<ApiResponse<DashboardActivityResponse>, {\n      limit?: number;\n    }>({\n      query: (params = {}) => ({\n        url: '/dashboard/activity',\n        params: {\n          limit: params.limit || 10,\n        },\n      }),\n      providesTags: ['Project', 'Terminology', 'TeamMember', 'TeamInvitation'],\n      // Refetch every 2 minutes for activity feed\n      keepUnusedDataFor: 120,\n    }),\n\n    getRecentProjects: builder.query<ApiResponse<RecentProjectsResponse>, {\n      limit?: number;\n    }>({\n      query: (params = {}) => ({\n        url: '/dashboard/recent-projects',\n        params: {\n          limit: params.limit || 5,\n        },\n      }),\n      providesTags: ['Project'],\n      // Refetch every 5 minutes\n      keepUnusedDataFor: 300,\n    }),\n\n    // Mutation to refresh dashboard data\n    refreshDashboard: builder.mutation<void, void>({\n      query: () => ({\n        url: '/dashboard/refresh',\n        method: 'POST',\n      }),\n      invalidatesTags: ['Project', 'Terminology', 'TeamMember', 'TeamInvitation'],\n    }),\n  }),\n  overrideExisting: true,\n});\n\n// Export hooks for use in components\nexport const {\n  useGetDashboardStatsQuery,\n  useGetDashboardActivityQuery,\n  useGetRecentProjectsQuery,\n  useRefreshDashboardMutation,\n} = dashboardApi;\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAmFO,MAAM,eAAe,4HAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAClD,WAAW,CAAC,UAAY,CAAC;YACvB,mBAAmB,QAAQ,KAAK,CAAoC;gBAClE,OAAO,IAAM;gBACb,cAAc;oBAAC;oBAAW;oBAAe;iBAAa;gBACtD,0BAA0B;gBAC1B,mBAAmB;YACrB;YAEA,sBAAsB,QAAQ,KAAK,CAEhC;gBACD,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,OAAO,OAAO,KAAK,IAAI;wBACzB;oBACF,CAAC;gBACD,cAAc;oBAAC;oBAAW;oBAAe;oBAAc;iBAAiB;gBACxE,4CAA4C;gBAC5C,mBAAmB;YACrB;YAEA,mBAAmB,QAAQ,KAAK,CAE7B;gBACD,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ;4BACN,OAAO,OAAO,KAAK,IAAI;wBACzB;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAU;gBACzB,0BAA0B;gBAC1B,mBAAmB;YACrB;YAEA,qCAAqC;YACrC,kBAAkB,QAAQ,QAAQ,CAAa;gBAC7C,OAAO,IAAM,CAAC;wBACZ,KAAK;wBACL,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;oBAAW;oBAAe;oBAAc;iBAAiB;YAC7E;QACF,CAAC;IACD,kBAAkB;AACpB;AAGO,MAAM,EACX,yBAAyB,EACzB,4BAA4B,EAC5B,yBAAyB,EACzB,2BAA2B,EAC5B,GAAG", "debugId": null}}, {"offset": {"line": 1614, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\nimport { DashboardLayout } from '@/components/layout/DashboardLayout';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport {\n  Loader2,\n  FolderOpen,\n  BookOpen,\n  Users,\n  TrendingUp,\n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Plus,\n  ArrowRight,\n  RefreshCw,\n  UserPlus\n} from 'lucide-react';\nimport {\n  useGetDashboardStatsQuery,\n  useGetDashboardActivityQuery,\n  useGetRecentProjectsQuery,\n  useRefreshDashboardMutation\n} from '@/lib/api/dashboard';\nimport { InviteTeamMemberDialog } from '@/components/team/InviteTeamMemberDialog';\n\nexport default function DashboardPage() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [showInviteDialog, setShowInviteDialog] = useState(false);\n\n  // RTK Query hooks for dashboard data\n  const {\n    data: statsResponse,\n    isLoading: statsLoading,\n    error: statsError,\n    refetch: refetchStats\n  } = useGetDashboardStatsQuery();\n\n  const {\n    data: activityResponse,\n    isLoading: activityLoading,\n    error: activityError,\n    refetch: refetchActivity\n  } = useGetDashboardActivityQuery({ limit: 10 });\n\n  const {\n    data: recentProjectsResponse,\n    isLoading: projectsLoading,\n    error: projectsError,\n    refetch: refetchProjects\n  } = useGetRecentProjectsQuery({ limit: 5 });\n\n  const [refreshDashboard, { isLoading: isRefreshing }] = useRefreshDashboardMutation();\n\n  // Extract data from API responses\n  const stats = statsResponse?.data;\n  const activities = activityResponse?.data?.activities || [];\n  const recentProjects = recentProjectsResponse?.data?.projects || [];\n\n  // Helper functions\n  const handleRefreshDashboard = async () => {\n    try {\n      await refreshDashboard().unwrap();\n      refetchStats();\n      refetchActivity();\n      refetchProjects();\n    } catch (error) {\n      console.error('Failed to refresh dashboard:', error);\n    }\n  };\n\n  const handleCreateProject = () => {\n    router.push('/dashboard/projects/new');\n  };\n\n  const handleManageTerminology = () => {\n    router.push('/dashboard/terminology');\n  };\n\n  const handleViewAllProjects = () => {\n    router.push('/dashboard/projects');\n  };\n\n  const handleViewAllActivity = () => {\n    // Could navigate to a dedicated activity page in the future\n    console.log('View all activity');\n  };\n\n  const getStatusBadgeVariant = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'default';\n      case 'review':\n      case 'in_progress':\n        return 'secondary';\n      case 'on_hold':\n        return 'outline';\n      default:\n        return 'outline';\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  const getActivityIcon = (type: string, action: string) => {\n    switch (type) {\n      case 'project':\n        return action === 'created' ? <Plus className=\"h-4 w-4\" /> : <FolderOpen className=\"h-4 w-4\" />;\n      case 'terminology':\n        return <BookOpen className=\"h-4 w-4\" />;\n      case 'team':\n        return <Users className=\"h-4 w-4\" />;\n      case 'invitation':\n        return <UserPlus className=\"h-4 w-4\" />;\n      default:\n        return <CheckCircle className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getActivityIconColor = (type: string) => {\n    switch (type) {\n      case 'project':\n        return 'text-blue-600 bg-blue-100';\n      case 'terminology':\n        return 'text-purple-600 bg-purple-100';\n      case 'team':\n        return 'text-green-600 bg-green-100';\n      case 'invitation':\n        return 'text-orange-600 bg-orange-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  useEffect(() => {\n    if (status === 'unauthenticated') {\n      router.push('/auth/signin');\n    }\n  }, [status, router]);\n\n  if (status === 'loading') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-4\" />\n          <p className=\"text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (status === 'unauthenticated') {\n    return null; // Will redirect\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Welcome Header */}\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            Welcome back, {session?.user?.name || 'User'}!\n          </h1>\n          <p className=\"text-gray-600\">\n            Here's what's happening with your translation projects today.\n          </p>\n        </div>\n\n        {/* Stats Overview */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <FolderOpen className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Total Projects</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{mockStats.totalProjects}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-green-100 rounded-lg\">\n                  <TrendingUp className=\"h-6 w-6 text-green-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Active Projects</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{mockStats.activeProjects}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-purple-100 rounded-lg\">\n                  <BookOpen className=\"h-6 w-6 text-purple-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Terminology</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{mockStats.totalTerms}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-orange-100 rounded-lg\">\n                  <Users className=\"h-6 w-6 text-orange-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Team Members</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{mockStats.teamMembers}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Recent Projects */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between\">\n              <div>\n                <CardTitle>Recent Projects</CardTitle>\n                <CardDescription>Your latest translation projects</CardDescription>\n              </div>\n              <Button size=\"sm\">\n                <Plus className=\"mr-2 h-4 w-4\" />\n                New Project\n              </Button>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {mockRecentProjects.map((project) => (\n                  <div key={project.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <h4 className=\"font-medium\">{project.name}</h4>\n                        <Badge\n                          variant={\n                            project.status === 'completed' ? 'default' :\n                            project.status === 'review' ? 'secondary' : 'outline'\n                          }\n                        >\n                          {project.status === 'in_progress' ? 'In Progress' :\n                           project.status === 'review' ? 'In Review' : 'Completed'}\n                        </Badge>\n                      </div>\n                      <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                        <span>Target: {project.language}</span>\n                        <span className=\"mx-2\">•</span>\n                        <span>Due: {project.dueDate}</span>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Progress value={project.progress} className=\"flex-1 mr-2\" />\n                        <span className=\"text-sm text-gray-600\">{project.progress}%</span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n              <div className=\"mt-4\">\n                <Button variant=\"ghost\" className=\"w-full\">\n                  View All Projects\n                  <ArrowRight className=\"ml-2 h-4 w-4\" />\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Activity Feed */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Recent Activity</CardTitle>\n              <CardDescription>Latest updates from your team</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"p-1 bg-green-100 rounded-full\">\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm\">\n                      <span className=\"font-medium\">Sarah Chen</span> completed translation for\n                      <span className=\"font-medium\"> Mobile App Localization</span>\n                    </p>\n                    <p className=\"text-xs text-gray-500\">2 hours ago</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"p-1 bg-blue-100 rounded-full\">\n                    <Clock className=\"h-4 w-4 text-blue-600\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm\">\n                      <span className=\"font-medium\">Mike Johnson</span> started review for\n                      <span className=\"font-medium\"> Website Translation</span>\n                    </p>\n                    <p className=\"text-xs text-gray-500\">4 hours ago</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"p-1 bg-orange-100 rounded-full\">\n                    <AlertCircle className=\"h-4 w-4 text-orange-600\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm\">\n                      <span className=\"font-medium\">Documentation Update</span> deadline approaching\n                    </p>\n                    <p className=\"text-xs text-gray-500\">6 hours ago</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"p-1 bg-purple-100 rounded-full\">\n                    <BookOpen className=\"h-4 w-4 text-purple-600\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm\">\n                      <span className=\"font-medium\">Emma Davis</span> added 15 new terms to\n                      <span className=\"font-medium\"> Technical Glossary</span>\n                    </p>\n                    <p className=\"text-xs text-gray-500\">1 day ago</p>\n                  </div>\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <Button variant=\"ghost\" className=\"w-full\">\n                  View All Activity\n                  <ArrowRight className=\"ml-2 h-4 w-4\" />\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Quick Actions */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Quick Actions</CardTitle>\n            <CardDescription>Get started with common tasks</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <Button className=\"h-20 flex-col\">\n                <Plus className=\"h-6 w-6 mb-2\" />\n                Create Project\n              </Button>\n              <Button variant=\"outline\" className=\"h-20 flex-col\">\n                <BookOpen className=\"h-6 w-6 mb-2\" />\n                Manage Terminology\n              </Button>\n              <Button variant=\"outline\" className=\"h-20 flex-col\">\n                <Users className=\"h-6 w-6 mb-2\" />\n                Invite Team Member\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AA1BA;;;;;;;;;;;AAkCe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qCAAqC;IACrC,MAAM,EACJ,MAAM,aAAa,EACnB,WAAW,YAAY,EACvB,OAAO,UAAU,EACjB,SAAS,YAAY,EACtB,GAAG,CAAA,GAAA,iIAAA,CAAA,4BAAyB,AAAD;IAE5B,MAAM,EACJ,MAAM,gBAAgB,EACtB,WAAW,eAAe,EAC1B,OAAO,aAAa,EACpB,SAAS,eAAe,EACzB,GAAG,CAAA,GAAA,iIAAA,CAAA,+BAA4B,AAAD,EAAE;QAAE,OAAO;IAAG;IAE7C,MAAM,EACJ,MAAM,sBAAsB,EAC5B,WAAW,eAAe,EAC1B,OAAO,aAAa,EACpB,SAAS,eAAe,EACzB,GAAG,CAAA,GAAA,iIAAA,CAAA,4BAAyB,AAAD,EAAE;QAAE,OAAO;IAAE;IAEzC,MAAM,CAAC,kBAAkB,EAAE,WAAW,YAAY,EAAE,CAAC,GAAG,CAAA,GAAA,iIAAA,CAAA,8BAA2B,AAAD;IAElF,kCAAkC;IAClC,MAAM,QAAQ,eAAe;IAC7B,MAAM,aAAa,kBAAkB,MAAM,cAAc,EAAE;IAC3D,MAAM,iBAAiB,wBAAwB,MAAM,YAAY,EAAE;IAEnE,mBAAmB;IACnB,MAAM,yBAAyB;QAC7B,IAAI;YACF,MAAM,mBAAmB,MAAM;YAC/B;YACA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,sBAAsB;QAC1B,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,0BAA0B;QAC9B,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,wBAAwB;QAC5B,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,wBAAwB;QAC5B,4DAA4D;QAC5D,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB;IAChD;IAEA,MAAM,kBAAkB,CAAC,MAAc;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO,WAAW,0BAAY,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;yCAAe,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YACrF,KAAK;gBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B;gBACE,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,WAAW,mBAAmB;gBAChC,OAAO,IAAI,CAAC;YACd;QACF;kCAAG;QAAC;QAAQ;KAAO;IAEnB,IAAI,WAAW,WAAW;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,WAAW,mBAAmB;QAChC,OAAO,MAAM,gBAAgB;IAC/B;IAEA,qBACE,6LAAC,kJAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;;gCAAmC;gCAChC,SAAS,MAAM,QAAQ;gCAAO;;;;;;;sCAE/C,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAM/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,UAAU,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhF,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMjF,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,UAAU,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM7E,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC;;8DACC,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAEnB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;;8DACX,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAIrC,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDACZ,mBAAmB,GAAG,CAAC,CAAC,wBACvB,6LAAC;oDAAqB,WAAU;8DAC9B,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAe,QAAQ,IAAI;;;;;;kFACzC,6LAAC,oIAAA,CAAA,QAAK;wEACJ,SACE,QAAQ,MAAM,KAAK,cAAc,YACjC,QAAQ,MAAM,KAAK,WAAW,cAAc;kFAG7C,QAAQ,MAAM,KAAK,gBAAgB,gBACnC,QAAQ,MAAM,KAAK,WAAW,cAAc;;;;;;;;;;;;0EAGjD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAK;4EAAS,QAAQ,QAAQ;;;;;;;kFAC/B,6LAAC;wEAAK,WAAU;kFAAO;;;;;;kFACvB,6LAAC;;4EAAK;4EAAM,QAAQ,OAAO;;;;;;;;;;;;;0EAE7B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uIAAA,CAAA,WAAQ;wEAAC,OAAO,QAAQ,QAAQ;wEAAE,WAAU;;;;;;kFAC7C,6LAAC;wEAAK,WAAU;;4EAAyB,QAAQ,QAAQ;4EAAC;;;;;;;;;;;;;;;;;;;mDArBtD,QAAQ,EAAE;;;;;;;;;;sDA2BxB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;;oDAAS;kEAEzC,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO9B,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;sEAEzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;sFACX,6LAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAiB;sFAC/C,6LAAC;4EAAK,WAAU;sFAAc;;;;;;;;;;;;8EAEhC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;sFACX,6LAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAmB;sFACjD,6LAAC;4EAAK,WAAU;sFAAc;;;;;;;;;;;;8EAEhC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;sEAEzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;sFACX,6LAAC;4EAAK,WAAU;sFAAc;;;;;;wEAA2B;;;;;;;8EAE3D,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;sFACX,6LAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAiB;sFAC/C,6LAAC;4EAAK,WAAU;sFAAc;;;;;;;;;;;;8EAEhC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAI3C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;;oDAAS;kEAEzC,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhC,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD;GA/VwB;;QACY,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS;QASpB,iIAAA,CAAA,4BAAyB;QAOzB,iIAAA,CAAA,+BAA4B;QAO5B,iIAAA,CAAA,4BAAyB;QAE2B,iIAAA,CAAA,8BAA2B;;;KA3B7D", "debugId": null}}]}