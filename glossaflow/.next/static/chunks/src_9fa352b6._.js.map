{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/accessibility/SkipLink.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface SkipLinkProps {\n  href: string;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function SkipLink({ href, children, className = '' }: SkipLinkProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  return (\n    <a\n      href={href}\n      className={`\n        fixed top-4 left-4 z-50 px-4 py-2 bg-blue-600 text-white rounded-md\n        transform transition-transform duration-200 ease-in-out\n        focus:translate-y-0 focus:opacity-100\n        ${isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'}\n        ${className}\n      `}\n      onFocus={() => setIsVisible(true)}\n      onBlur={() => setIsVisible(false)}\n    >\n      {children}\n    </a>\n  );\n}\n\nexport function SkipLinks() {\n  return (\n    <>\n      <SkipLink href=\"#main-content\">Skip to main content</SkipLink>\n      <SkipLink href=\"#navigation\">Skip to navigation</SkipLink>\n      <SkipLink href=\"#search\">Skip to search</SkipLink>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAUO,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAiB;;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAC;;;;QAIV,EAAE,YAAY,8BAA8B,8BAA8B;QAC1E,EAAE,UAAU;MACd,CAAC;QACD,SAAS,IAAM,aAAa;QAC5B,QAAQ,IAAM,aAAa;kBAE1B;;;;;;AAGP;GAnBgB;KAAA;AAqBT,SAAS;IACd,qBACE;;0BACE,6LAAC;gBAAS,MAAK;0BAAgB;;;;;;0BAC/B,6LAAC;gBAAS,MAAK;0BAAc;;;;;;0BAC7B,6LAAC;gBAAS,MAAK;0BAAU;;;;;;;;AAG/B;MARgB", "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/accessibility/ScreenReaderOnly.tsx"], "sourcesContent": ["interface ScreenReaderOnlyProps {\n  children: React.ReactNode;\n  as?: keyof JSX.IntrinsicElements;\n  className?: string;\n}\n\nexport function ScreenReaderOnly({ \n  children, \n  as: Component = 'span',\n  className = '' \n}: ScreenReaderOnlyProps) {\n  return (\n    <Component \n      className={`sr-only ${className}`}\n      aria-hidden=\"false\"\n    >\n      {children}\n    </Component>\n  );\n}\n\n// Utility component for live regions\ninterface LiveRegionProps {\n  children: React.ReactNode;\n  priority?: 'polite' | 'assertive';\n  atomic?: boolean;\n  relevant?: 'additions' | 'removals' | 'text' | 'all';\n}\n\nexport function LiveRegion({ \n  children, \n  priority = 'polite',\n  atomic = true,\n  relevant = 'all'\n}: LiveRegionProps) {\n  return (\n    <div\n      aria-live={priority}\n      aria-atomic={atomic}\n      aria-relevant={relevant}\n      className=\"sr-only\"\n    >\n      {children}\n    </div>\n  );\n}\n\n// Component for status messages\ninterface StatusMessageProps {\n  message: string;\n  type?: 'success' | 'error' | 'warning' | 'info';\n  visible?: boolean;\n}\n\nexport function StatusMessage({ \n  message, \n  type = 'info',\n  visible = true \n}: StatusMessageProps) {\n  if (!visible) return null;\n\n  const priority = type === 'error' ? 'assertive' : 'polite';\n\n  return (\n    <LiveRegion priority={priority}>\n      <span role=\"status\" aria-label={`${type}: ${message}`}>\n        {message}\n      </span>\n    </LiveRegion>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAMO,SAAS,iBAAiB,EAC/B,QAAQ,EACR,IAAI,YAAY,MAAM,EACtB,YAAY,EAAE,EACQ;IACtB,qBACE,6LAAC;QACC,WAAW,CAAC,QAAQ,EAAE,WAAW;QACjC,eAAY;kBAEX;;;;;;AAGP;KAbgB;AAuBT,SAAS,WAAW,EACzB,QAAQ,EACR,WAAW,QAAQ,EACnB,SAAS,IAAI,EACb,WAAW,KAAK,EACA;IAChB,qBACE,6LAAC;QACC,aAAW;QACX,eAAa;QACb,iBAAe;QACf,WAAU;kBAET;;;;;;AAGP;MAhBgB;AAyBT,SAAS,cAAc,EAC5B,OAAO,EACP,OAAO,MAAM,EACb,UAAU,IAAI,EACK;IACnB,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,WAAW,SAAS,UAAU,cAAc;IAElD,qBACE,6LAAC;QAAW,UAAU;kBACpB,cAAA,6LAAC;YAAK,MAAK;YAAS,cAAY,GAAG,KAAK,EAAE,EAAE,SAAS;sBAClD;;;;;;;;;;;AAIT;MAhBgB", "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useSession, signOut } from 'next-auth/react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';\nimport { SkipLinks } from '@/components/accessibility/SkipLink';\nimport { ScreenReaderOnly } from '@/components/accessibility/ScreenReaderOnly';\nimport {\n  Home,\n  FolderOpen,\n  BookOpen,\n  Users,\n  Settings,\n  LogOut,\n  Menu,\n  Bell,\n  Search,\n  Plus,\n} from 'lucide-react';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Projects', href: '/dashboard/projects', icon: FolderOpen },\n  { name: 'Terminology', href: '/dashboard/terminology', icon: BookOpen },\n  { name: 'Team', href: '/dashboard/team', icon: Users },\n  { name: 'Settings', href: '/dashboard/settings', icon: Settings },\n];\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const { data: session } = useSession();\n  const pathname = usePathname();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <SkipLinks />\n\n      {/* Mobile sidebar */}\n      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>\n        <SheetContent side=\"left\" className=\"w-64 p-0\">\n          <div className=\"flex h-full flex-col\">\n            <div className=\"flex h-16 items-center px-6 border-b\">\n              <Link href=\"/dashboard\" className=\"flex items-center\">\n                <span className=\"text-xl font-bold text-blue-600\">GlossaFlow</span>\n                <ScreenReaderOnly>- Translation Management Platform</ScreenReaderOnly>\n              </Link>\n            </div>\n            <nav\n              className=\"flex-1 space-y-1 px-3 py-4\"\n              id=\"navigation\"\n              role=\"navigation\"\n              aria-label=\"Main navigation\"\n            >\n              {navigation.map((item) => {\n                const isActive = pathname === item.href;\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-blue-100 text-blue-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                    aria-current={isActive ? 'page' : undefined}\n                  >\n                    <item.icon className=\"mr-3 h-5 w-5\" aria-hidden=\"true\" />\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n        </SheetContent>\n      </Sheet>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 items-center px-6 border-b\">\n            <Link href=\"/dashboard\" className=\"flex items-center\">\n              <span className=\"text-xl font-bold text-blue-600\">GlossaFlow</span>\n            </Link>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-3 py-4\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                    isActive\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-700 hover:bg-gray-100'\n                  }`}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top navigation */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1 items-center\">\n              <Search className=\"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 pl-3\" />\n              <input\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm\"\n                placeholder=\"Search projects, terminology...\"\n                type=\"search\"\n              />\n            </div>\n            <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n              <Button size=\"sm\" className=\"hidden sm:flex\">\n                <Plus className=\"mr-2 h-4 w-4\" />\n                New Project\n              </Button>\n\n              <Button variant=\"ghost\" size=\"sm\">\n                <Bell className=\"h-5 w-5\" />\n              </Button>\n\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarImage src={session?.user?.image || ''} alt={session?.user?.name || ''} />\n                      <AvatarFallback>\n                        {session?.user?.name?.charAt(0) || session?.user?.email?.charAt(0) || 'U'}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <DropdownMenuLabel className=\"font-normal\">\n                    <div className=\"flex flex-col space-y-1\">\n                      <p className=\"text-sm font-medium leading-none\">{session?.user?.name}</p>\n                      <p className=\"text-xs leading-none text-muted-foreground\">\n                        {session?.user?.email}\n                      </p>\n                    </div>\n                  </DropdownMenuLabel>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/profile\">Profile</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/settings\">Settings</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem\n                    className=\"text-red-600\"\n                    onClick={() => signOut({ callbackUrl: '/auth/signin' })}\n                  >\n                    <LogOut className=\"mr-2 h-4 w-4\" />\n                    Sign out\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main\n          className=\"py-6\"\n          id=\"main-content\"\n          role=\"main\"\n          aria-label=\"Main content\"\n        >\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAnBA;;;;;;;;;;;;AAoCA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,sMAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,qNAAA,CAAA,aAAU;IAAC;IAClE;QAAE,MAAM;QAAe,MAAM;QAA0B,MAAM,iNAAA,CAAA,WAAQ;IAAC;IACtE;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,uMAAA,CAAA,QAAK;IAAC;IACrD;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,6MAAA,CAAA,WAAQ;IAAC;CACjE;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IAChE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,kJAAA,CAAA,YAAS;;;;;0BAGV,6LAAC,oIAAA,CAAA,QAAK;gBAAC,MAAM;gBAAa,cAAc;0BACtC,cAAA,6LAAC,oIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAO,WAAU;8BAClC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;;sDAChC,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;sDAClD,6LAAC,0JAAA,CAAA,mBAAgB;sDAAC;;;;;;;;;;;;;;;;;0CAGtB,6LAAC;gCACC,WAAU;gCACV,IAAG;gCACH,MAAK;gCACL,cAAW;0CAEV,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,mCACJ;wCACF,SAAS,IAAM,eAAe;wCAC9B,gBAAc,WAAW,SAAS;;0DAElC,6LAAC,KAAK,IAAI;gDAAC,WAAU;gDAAe,eAAY;;;;;;4CAC/C,KAAK,IAAI;;uCAXL,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;;;;;;;;;;;;0BAOR,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAChC,cAAA,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;sCAGtD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,mCACJ;;sDAEF,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCATL,KAAK,IAAI;;;;;4BAYpB;;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDACC,WAAU;gDACV,aAAY;gDACZ,MAAK;;;;;;;;;;;;kDAGT,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;;kEAC1B,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAInC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAGlB,6LAAC,+IAAA,CAAA,eAAY;;kEACX,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,WAAU;sEAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,6LAAC,qIAAA,CAAA,cAAW;wEAAC,KAAK,SAAS,MAAM,SAAS;wEAAI,KAAK,SAAS,MAAM,QAAQ;;;;;;kFAC1E,6LAAC,qIAAA,CAAA,iBAAc;kFACZ,SAAS,MAAM,MAAM,OAAO,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;kEAK9E,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,WAAU;wDAAO,OAAM;wDAAM,UAAU;;0EAC1D,6LAAC,+IAAA,CAAA,oBAAiB;gEAAC,WAAU;0EAC3B,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAoC,SAAS,MAAM;;;;;;sFAChE,6LAAC;4EAAE,WAAU;sFACV,SAAS,MAAM;;;;;;;;;;;;;;;;;0EAItB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0EACtB,6LAAC,+IAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;8EAAqB;;;;;;;;;;;0EAElC,6LAAC,+IAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;8EAAsB;;;;;;;;;;;0EAEnC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0EACtB,6LAAC,+IAAA,CAAA,mBAAgB;gEACf,WAAU;gEACV,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;wEAAE,aAAa;oEAAe;;kFAErD,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/C,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,MAAK;wBACL,cAAW;kCAEX,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAtKgB;;QACY,iJAAA,CAAA,aAAU;QACnB,qIAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1441, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 1538, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1570, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 1601, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 1676, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1874, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1908, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1943, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 2192, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport {\n  ChevronDownIcon,\n  ChevronLeftIcon,\n  ChevronRightIcon,\n} from \"lucide-react\"\nimport { DayButton, DayPicker, getDefaultClassNames } from \"react-day-picker\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Button, buttonVariants } from \"@/components/ui/button\"\n\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  captionLayout = \"label\",\n  buttonVariant = \"ghost\",\n  formatters,\n  components,\n  ...props\n}: React.ComponentProps<typeof DayPicker> & {\n  buttonVariant?: React.ComponentProps<typeof Button>[\"variant\"]\n}) {\n  const defaultClassNames = getDefaultClassNames()\n\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn(\n        \"bg-background group/calendar p-3 [--cell-size:--spacing(8)] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent\",\n        String.raw`rtl:**:[.rdp-button\\_next>svg]:rotate-180`,\n        String.raw`rtl:**:[.rdp-button\\_previous>svg]:rotate-180`,\n        className\n      )}\n      captionLayout={captionLayout}\n      formatters={{\n        formatMonthDropdown: (date) =>\n          date.toLocaleString(\"default\", { month: \"short\" }),\n        ...formatters,\n      }}\n      classNames={{\n        root: cn(\"w-fit\", defaultClassNames.root),\n        months: cn(\n          \"flex gap-4 flex-col md:flex-row relative\",\n          defaultClassNames.months\n        ),\n        month: cn(\"flex flex-col w-full gap-4\", defaultClassNames.month),\n        nav: cn(\n          \"flex items-center gap-1 w-full absolute top-0 inset-x-0 justify-between\",\n          defaultClassNames.nav\n        ),\n        button_previous: cn(\n          buttonVariants({ variant: buttonVariant }),\n          \"size-(--cell-size) aria-disabled:opacity-50 p-0 select-none\",\n          defaultClassNames.button_previous\n        ),\n        button_next: cn(\n          buttonVariants({ variant: buttonVariant }),\n          \"size-(--cell-size) aria-disabled:opacity-50 p-0 select-none\",\n          defaultClassNames.button_next\n        ),\n        month_caption: cn(\n          \"flex items-center justify-center h-(--cell-size) w-full px-(--cell-size)\",\n          defaultClassNames.month_caption\n        ),\n        dropdowns: cn(\n          \"w-full flex items-center text-sm font-medium justify-center h-(--cell-size) gap-1.5\",\n          defaultClassNames.dropdowns\n        ),\n        dropdown_root: cn(\n          \"relative has-focus:border-ring border border-input shadow-xs has-focus:ring-ring/50 has-focus:ring-[3px] rounded-md\",\n          defaultClassNames.dropdown_root\n        ),\n        dropdown: cn(\"absolute inset-0 opacity-0\", defaultClassNames.dropdown),\n        caption_label: cn(\n          \"select-none font-medium\",\n          captionLayout === \"label\"\n            ? \"text-sm\"\n            : \"rounded-md pl-2 pr-1 flex items-center gap-1 text-sm h-8 [&>svg]:text-muted-foreground [&>svg]:size-3.5\",\n          defaultClassNames.caption_label\n        ),\n        table: \"w-full border-collapse\",\n        weekdays: cn(\"flex\", defaultClassNames.weekdays),\n        weekday: cn(\n          \"text-muted-foreground rounded-md flex-1 font-normal text-[0.8rem] select-none\",\n          defaultClassNames.weekday\n        ),\n        week: cn(\"flex w-full mt-2\", defaultClassNames.week),\n        week_number_header: cn(\n          \"select-none w-(--cell-size)\",\n          defaultClassNames.week_number_header\n        ),\n        week_number: cn(\n          \"text-[0.8rem] select-none text-muted-foreground\",\n          defaultClassNames.week_number\n        ),\n        day: cn(\n          \"relative w-full h-full p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-md [&:last-child[data-selected=true]_button]:rounded-r-md group/day aspect-square select-none\",\n          defaultClassNames.day\n        ),\n        range_start: cn(\n          \"rounded-l-md bg-accent\",\n          defaultClassNames.range_start\n        ),\n        range_middle: cn(\"rounded-none\", defaultClassNames.range_middle),\n        range_end: cn(\"rounded-r-md bg-accent\", defaultClassNames.range_end),\n        today: cn(\n          \"bg-accent text-accent-foreground rounded-md data-[selected=true]:rounded-none\",\n          defaultClassNames.today\n        ),\n        outside: cn(\n          \"text-muted-foreground aria-selected:text-muted-foreground\",\n          defaultClassNames.outside\n        ),\n        disabled: cn(\n          \"text-muted-foreground opacity-50\",\n          defaultClassNames.disabled\n        ),\n        hidden: cn(\"invisible\", defaultClassNames.hidden),\n        ...classNames,\n      }}\n      components={{\n        Root: ({ className, rootRef, ...props }) => {\n          return (\n            <div\n              data-slot=\"calendar\"\n              ref={rootRef}\n              className={cn(className)}\n              {...props}\n            />\n          )\n        },\n        Chevron: ({ className, orientation, ...props }) => {\n          if (orientation === \"left\") {\n            return (\n              <ChevronLeftIcon className={cn(\"size-4\", className)} {...props} />\n            )\n          }\n\n          if (orientation === \"right\") {\n            return (\n              <ChevronRightIcon\n                className={cn(\"size-4\", className)}\n                {...props}\n              />\n            )\n          }\n\n          return (\n            <ChevronDownIcon className={cn(\"size-4\", className)} {...props} />\n          )\n        },\n        DayButton: CalendarDayButton,\n        WeekNumber: ({ children, ...props }) => {\n          return (\n            <td {...props}>\n              <div className=\"flex size-(--cell-size) items-center justify-center text-center\">\n                {children}\n              </div>\n            </td>\n          )\n        },\n        ...components,\n      }}\n      {...props}\n    />\n  )\n}\n\nfunction CalendarDayButton({\n  className,\n  day,\n  modifiers,\n  ...props\n}: React.ComponentProps<typeof DayButton>) {\n  const defaultClassNames = getDefaultClassNames()\n\n  const ref = React.useRef<HTMLButtonElement>(null)\n  React.useEffect(() => {\n    if (modifiers.focused) ref.current?.focus()\n  }, [modifiers.focused])\n\n  return (\n    <Button\n      ref={ref}\n      variant=\"ghost\"\n      size=\"icon\"\n      data-day={day.date.toLocaleDateString()}\n      data-selected-single={\n        modifiers.selected &&\n        !modifiers.range_start &&\n        !modifiers.range_end &&\n        !modifiers.range_middle\n      }\n      data-range-start={modifiers.range_start}\n      data-range-end={modifiers.range_end}\n      data-range-middle={modifiers.range_middle}\n      className={cn(\n        \"data-[selected-single=true]:bg-primary data-[selected-single=true]:text-primary-foreground data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary data-[range-start=true]:text-primary-foreground data-[range-end=true]:bg-primary data-[range-end=true]:text-primary-foreground group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 dark:hover:text-accent-foreground flex aspect-square size-auto w-full min-w-(--cell-size) flex-col gap-1 leading-none font-normal group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] data-[range-end=true]:rounded-md data-[range-end=true]:rounded-r-md data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-md data-[range-start=true]:rounded-l-md [&>span]:text-xs [&>span]:opacity-70\",\n        defaultClassNames.day,\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Calendar, CalendarDayButton }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAKA;AAAA;AAEA;AACA;;;AAXA;;;;;;AAaA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,gBAAgB,OAAO,EACvB,gBAAgB,OAAO,EACvB,UAAU,EACV,UAAU,EACV,GAAG,OAGJ;IACC,MAAM,oBAAoB,CAAA,GAAA,2LAAA,CAAA,uBAAoB,AAAD;IAE7C,qBACE,6LAAC,qKAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA,OAAO,GAAG,CAAC,yCAAyC,CAAC,EACrD,OAAO,GAAG,CAAC,6CAA6C,CAAC,EACzD;QAEF,eAAe;QACf,YAAY;YACV,qBAAqB,CAAC,OACpB,KAAK,cAAc,CAAC,WAAW;oBAAE,OAAO;gBAAQ;YAClD,GAAG,UAAU;QACf;QACA,YAAY;YACV,MAAM,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,kBAAkB,IAAI;YACxC,QAAQ,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACP,4CACA,kBAAkB,MAAM;YAE1B,OAAO,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,kBAAkB,KAAK;YAC/D,KAAK,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACJ,2EACA,kBAAkB,GAAG;YAEvB,iBAAiB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAc,IACxC,+DACA,kBAAkB,eAAe;YAEnC,aAAa,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACZ,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAc,IACxC,+DACA,kBAAkB,WAAW;YAE/B,eAAe,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,4EACA,kBAAkB,aAAa;YAEjC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uFACA,kBAAkB,SAAS;YAE7B,eAAe,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,uHACA,kBAAkB,aAAa;YAEjC,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,kBAAkB,QAAQ;YACrE,eAAe,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,2BACA,kBAAkB,UACd,YACA,2GACJ,kBAAkB,aAAa;YAEjC,OAAO;YACP,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,kBAAkB,QAAQ;YAC/C,SAAS,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,iFACA,kBAAkB,OAAO;YAE3B,MAAM,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB,kBAAkB,IAAI;YACnD,oBAAoB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACnB,+BACA,kBAAkB,kBAAkB;YAEtC,aAAa,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACZ,mDACA,kBAAkB,WAAW;YAE/B,KAAK,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACJ,6LACA,kBAAkB,GAAG;YAEvB,aAAa,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACZ,0BACA,kBAAkB,WAAW;YAE/B,cAAc,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,kBAAkB,YAAY;YAC/D,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B,kBAAkB,SAAS;YACnE,OAAO,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACN,iFACA,kBAAkB,KAAK;YAEzB,SAAS,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,6DACA,kBAAkB,OAAO;YAE3B,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACT,oCACA,kBAAkB,QAAQ;YAE5B,QAAQ,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,kBAAkB,MAAM;YAChD,GAAG,UAAU;QACf;QACA,YAAY;YACV,MAAM,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;gBACrC,qBACE,6LAAC;oBACC,aAAU;oBACV,KAAK;oBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;oBACb,GAAG,KAAK;;;;;;YAGf;YACA,SAAS,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,OAAO;gBAC5C,IAAI,gBAAgB,QAAQ;oBAC1B,qBACE,6LAAC,2NAAA,CAAA,kBAAe;wBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;wBAAa,GAAG,KAAK;;;;;;gBAElE;gBAEA,IAAI,gBAAgB,SAAS;oBAC3B,qBACE,6LAAC,6NAAA,CAAA,mBAAgB;wBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;wBACvB,GAAG,KAAK;;;;;;gBAGf;gBAEA,qBACE,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;YAElE;YACA,WAAW;YACX,YAAY,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;gBACjC,qBACE,6LAAC;oBAAI,GAAG,KAAK;8BACX,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;YAIT;YACA,GAAG,UAAU;QACf;QACC,GAAG,KAAK;;;;;;AAGf;KA5JS;AA8JT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,EACH,SAAS,EACT,GAAG,OACoC;;IACvC,MAAM,oBAAoB,CAAA,GAAA,2LAAA,CAAA,uBAAoB,AAAD;IAE7C,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAqB;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;uCAAE;YACd,IAAI,UAAU,OAAO,EAAE,IAAI,OAAO,EAAE;QACtC;sCAAG;QAAC,UAAU,OAAO;KAAC;IAEtB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,KAAK;QACL,SAAQ;QACR,MAAK;QACL,YAAU,IAAI,IAAI,CAAC,kBAAkB;QACrC,wBACE,UAAU,QAAQ,IAClB,CAAC,UAAU,WAAW,IACtB,CAAC,UAAU,SAAS,IACpB,CAAC,UAAU,YAAY;QAEzB,oBAAkB,UAAU,WAAW;QACvC,kBAAgB,UAAU,SAAS;QACnC,qBAAmB,UAAU,YAAY;QACzC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,o3BACA,kBAAkB,GAAG,EACrB;QAED,GAAG,KAAK;;;;;;AAGf;GApCS;MAAA", "debugId": null}}, {"offset": {"line": 2371, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\n}\n\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\n}\n\nfunction PopoverContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n  return (\n    <PopoverPrimitive.Portal>\n      <PopoverPrimitive.Content\n        data-slot=\"popover-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\n          className\n        )}\n        {...props}\n      />\n    </PopoverPrimitive.Portal>\n  )\n}\n\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\n}\n\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,6LAAC,sKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,sKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 2452, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/projects/CreateProjectDialog.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select';\nimport { Calendar } from '@/components/ui/calendar';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { CalendarIcon, Loader2, Plus } from 'lucide-react';\nimport { format } from 'date-fns';\nimport { cn } from '@/lib/utils';\n\nconst createProjectSchema = z.object({\n  name: z.string().min(1, 'Project name is required'),\n  description: z.string().optional(),\n  sourceLanguage: z.string().min(1, 'Source language is required'),\n  targetLanguages: z.array(z.string()).min(1, 'At least one target language is required'),\n  deadline: z.date().optional(),\n  priority: z.enum(['low', 'medium', 'high', 'urgent']),\n  category: z.string().min(1, 'Category is required'),\n});\n\ntype CreateProjectFormData = z.infer<typeof createProjectSchema>;\n\ninterface CreateProjectDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  onSubmit: (data: CreateProjectFormData) => void;\n}\n\nconst languages = [\n  'English',\n  'Spanish',\n  'French',\n  'German',\n  'Italian',\n  'Portuguese',\n  'Russian',\n  'Chinese (Simplified)',\n  'Chinese (Traditional)',\n  'Japanese',\n  'Korean',\n  'Arabic',\n  'Dutch',\n  'Swedish',\n  'Norwegian',\n];\n\nconst categories = [\n  'Website',\n  'Mobile App',\n  'Documentation',\n  'Marketing',\n  'Legal',\n  'Technical',\n  'Medical',\n  'Financial',\n  'E-commerce',\n  'Gaming',\n  'Other',\n];\n\nconst priorities = [\n  { value: 'low', label: 'Low', color: 'text-gray-600' },\n  { value: 'medium', label: 'Medium', color: 'text-blue-600' },\n  { value: 'high', label: 'High', color: 'text-orange-600' },\n  { value: 'urgent', label: 'Urgent', color: 'text-red-600' },\n];\n\nexport function CreateProjectDialog({ open, onOpenChange, onSubmit }: CreateProjectDialogProps) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [selectedTargetLanguages, setSelectedTargetLanguages] = useState<string[]>([]);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    setValue,\n    watch,\n    reset,\n  } = useForm<CreateProjectFormData>({\n    resolver: zodResolver(createProjectSchema),\n    defaultValues: {\n      priority: 'medium',\n      targetLanguages: [],\n    },\n  });\n\n  const sourceLanguage = watch('sourceLanguage');\n  const deadline = watch('deadline');\n  const priority = watch('priority');\n\n  const handleFormSubmit = async (data: CreateProjectFormData) => {\n    setIsLoading(true);\n    try {\n      await onSubmit({\n        ...data,\n        targetLanguages: selectedTargetLanguages,\n      });\n      reset();\n      setSelectedTargetLanguages([]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleClose = () => {\n    reset();\n    setSelectedTargetLanguages([]);\n    onOpenChange(false);\n  };\n\n  const addTargetLanguage = (language: string) => {\n    if (!selectedTargetLanguages.includes(language) && language !== sourceLanguage) {\n      const newLanguages = [...selectedTargetLanguages, language];\n      setSelectedTargetLanguages(newLanguages);\n      setValue('targetLanguages', newLanguages);\n    }\n  };\n\n  const removeTargetLanguage = (language: string) => {\n    const newLanguages = selectedTargetLanguages.filter(lang => lang !== language);\n    setSelectedTargetLanguages(newLanguages);\n    setValue('targetLanguages', newLanguages);\n  };\n\n  const availableTargetLanguages = languages.filter(\n    lang => lang !== sourceLanguage && !selectedTargetLanguages.includes(lang)\n  );\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-[600px] max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle>Create New Project</DialogTitle>\n          <DialogDescription>\n            Set up a new translation project with source and target languages.\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-6\">\n          {/* Project Name */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"name\">Project Name *</Label>\n            <Input\n              id=\"name\"\n              placeholder=\"Enter project name\"\n              {...register('name')}\n              disabled={isLoading}\n            />\n            {errors.name && (\n              <p className=\"text-sm text-red-600\">{errors.name.message}</p>\n            )}\n          </div>\n\n          {/* Description */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"description\">Description</Label>\n            <Textarea\n              id=\"description\"\n              placeholder=\"Describe your project...\"\n              {...register('description')}\n              disabled={isLoading}\n            />\n          </div>\n\n          {/* Languages */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>Source Language *</Label>\n              <Select onValueChange={(value) => setValue('sourceLanguage', value)}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select source language\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {languages.map((language) => (\n                    <SelectItem key={language} value={language}>\n                      {language}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n              {errors.sourceLanguage && (\n                <p className=\"text-sm text-red-600\">{errors.sourceLanguage.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>Target Languages *</Label>\n              <Select onValueChange={addTargetLanguage}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Add target language\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {availableTargetLanguages.map((language) => (\n                    <SelectItem key={language} value={language}>\n                      {language}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n              {selectedTargetLanguages.length > 0 && (\n                <div className=\"flex flex-wrap gap-2 mt-2\">\n                  {selectedTargetLanguages.map((language) => (\n                    <div\n                      key={language}\n                      className=\"flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm\"\n                    >\n                      {language}\n                      <button\n                        type=\"button\"\n                        onClick={() => removeTargetLanguage(language)}\n                        className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                      >\n                        ×\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              )}\n              {errors.targetLanguages && (\n                <p className=\"text-sm text-red-600\">{errors.targetLanguages.message}</p>\n              )}\n            </div>\n          </div>\n\n          {/* Category and Priority */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>Category *</Label>\n              <Select onValueChange={(value) => setValue('category', value)}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select category\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {categories.map((category) => (\n                    <SelectItem key={category} value={category}>\n                      {category}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n              {errors.category && (\n                <p className=\"text-sm text-red-600\">{errors.category.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>Priority</Label>\n              <Select \n                defaultValue=\"medium\"\n                onValueChange={(value) => setValue('priority', value as any)}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  {priorities.map((priority) => (\n                    <SelectItem key={priority.value} value={priority.value}>\n                      <span className={priority.color}>{priority.label}</span>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          {/* Deadline */}\n          <div className=\"space-y-2\">\n            <Label>Deadline (Optional)</Label>\n            <Popover>\n              <PopoverTrigger asChild>\n                <Button\n                  variant=\"outline\"\n                  className={cn(\n                    'w-full justify-start text-left font-normal',\n                    !deadline && 'text-muted-foreground'\n                  )}\n                >\n                  <CalendarIcon className=\"mr-2 h-4 w-4\" />\n                  {deadline ? format(deadline, 'PPP') : 'Pick a date'}\n                </Button>\n              </PopoverTrigger>\n              <PopoverContent className=\"w-auto p-0\">\n                <Calendar\n                  mode=\"single\"\n                  selected={deadline}\n                  onSelect={(date) => setValue('deadline', date)}\n                  initialFocus\n                />\n              </PopoverContent>\n            </Popover>\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex justify-end space-x-2\">\n            <Button type=\"button\" variant=\"outline\" onClick={handleClose} disabled={isLoading}>\n              Cancel\n            </Button>\n            <Button type=\"submit\" disabled={isLoading}>\n              {isLoading ? (\n                <>\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                  Creating...\n                </>\n              ) : (\n                <>\n                  <Plus className=\"mr-2 h-4 w-4\" />\n                  Create Project\n                </>\n              )}\n            </Button>\n          </div>\n        </form>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AA5BA;;;;;;;;;;;;;;;;AA8BA,MAAM,sBAAsB,CAAA,GAAA,qIAAA,CAAA,SAAQ,AAAD,EAAE;IACnC,MAAM,CAAA,GAAA,qIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IACxB,aAAa,CAAA,GAAA,qIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IAChC,gBAAgB,CAAA,GAAA,qIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAClC,iBAAiB,CAAA,GAAA,qIAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,SAAQ,AAAD,KAAK,GAAG,CAAC,GAAG;IAC5C,UAAU,CAAA,GAAA,qIAAA,CAAA,OAAM,AAAD,IAAI,QAAQ;IAC3B,UAAU,CAAA,GAAA,qIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAO;QAAU;QAAQ;KAAS;IACpD,UAAU,CAAA,GAAA,qIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;AAC9B;AAUA,MAAM,YAAY;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,aAAa;IACjB;QAAE,OAAO;QAAO,OAAO;QAAO,OAAO;IAAgB;IACrD;QAAE,OAAO;QAAU,OAAO;QAAU,OAAO;IAAgB;IAC3D;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAAkB;IACzD;QAAE,OAAO;QAAU,OAAO;QAAU,OAAO;IAAe;CAC3D;AAEM,SAAS,oBAAoB,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAA4B;;IAC5F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnF,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACR,KAAK,EACL,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAyB;QACjC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,UAAU;YACV,iBAAiB,EAAE;QACrB;IACF;IAEA,MAAM,iBAAiB,MAAM;IAC7B,MAAM,WAAW,MAAM;IACvB,MAAM,WAAW,MAAM;IAEvB,MAAM,mBAAmB,OAAO;QAC9B,aAAa;QACb,IAAI;YACF,MAAM,SAAS;gBACb,GAAG,IAAI;gBACP,iBAAiB;YACnB;YACA;YACA,2BAA2B,EAAE;QAC/B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB;QACA,2BAA2B,EAAE;QAC7B,aAAa;IACf;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,wBAAwB,QAAQ,CAAC,aAAa,aAAa,gBAAgB;YAC9E,MAAM,eAAe;mBAAI;gBAAyB;aAAS;YAC3D,2BAA2B;YAC3B,SAAS,mBAAmB;QAC9B;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,eAAe,wBAAwB,MAAM,CAAC,CAAA,OAAQ,SAAS;QACrE,2BAA2B;QAC3B,SAAS,mBAAmB;IAC9B;IAEA,MAAM,2BAA2B,UAAU,MAAM,CAC/C,CAAA,OAAQ,SAAS,kBAAkB,CAAC,wBAAwB,QAAQ,CAAC;IAGvE,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,6LAAC,qIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,6LAAC;oBAAK,UAAU,aAAa;oBAAmB,WAAU;;sCAExD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,aAAY;oCACX,GAAG,SAAS,OAAO;oCACpB,UAAU;;;;;;gCAEX,OAAO,IAAI,kBACV,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;sCAK5D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAc;;;;;;8CAC7B,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACX,GAAG,SAAS,cAAc;oCAC3B,UAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,6LAAC,qIAAA,CAAA,SAAM;4CAAC,eAAe,CAAC,QAAU,SAAS,kBAAkB;;8DAC3D,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;8DACX,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC,qIAAA,CAAA,aAAU;4DAAgB,OAAO;sEAC/B;2DADc;;;;;;;;;;;;;;;;wCAMtB,OAAO,cAAc,kBACpB,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;8CAItE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,6LAAC,qIAAA,CAAA,SAAM;4CAAC,eAAe;;8DACrB,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;8DACX,yBAAyB,GAAG,CAAC,CAAC,yBAC7B,6LAAC,qIAAA,CAAA,aAAU;4DAAgB,OAAO;sEAC/B;2DADc;;;;;;;;;;;;;;;;wCAMtB,wBAAwB,MAAM,GAAG,mBAChC,6LAAC;4CAAI,WAAU;sDACZ,wBAAwB,GAAG,CAAC,CAAC,yBAC5B,6LAAC;oDAEC,WAAU;;wDAET;sEACD,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,qBAAqB;4DACpC,WAAU;sEACX;;;;;;;mDARI;;;;;;;;;;wCAeZ,OAAO,eAAe,kBACrB,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;;;;;;;sCAMzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,6LAAC,qIAAA,CAAA,SAAM;4CAAC,eAAe,CAAC,QAAU,SAAS,YAAY;;8DACrD,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;8DACX,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,qIAAA,CAAA,aAAU;4DAAgB,OAAO;sEAC/B;2DADc;;;;;;;;;;;;;;;;wCAMtB,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;8CAIhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,6LAAC,qIAAA,CAAA,SAAM;4CACL,cAAa;4CACb,eAAe,CAAC,QAAU,SAAS,YAAY;;8DAE/C,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,6LAAC,qIAAA,CAAA,gBAAa;8DACX,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,qIAAA,CAAA,aAAU;4DAAsB,OAAO,SAAS,KAAK;sEACpD,cAAA,6LAAC;gEAAK,WAAW,SAAS,KAAK;0EAAG,SAAS,KAAK;;;;;;2DADjC,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC,sIAAA,CAAA,UAAO;;sDACN,6LAAC,sIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,YAAY;;kEAGf,6LAAC,iNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDACvB,WAAW,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,UAAU,SAAS;;;;;;;;;;;;sDAG1C,6LAAC,sIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACxB,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDACP,MAAK;gDACL,UAAU;gDACV,UAAU,CAAC,OAAS,SAAS,YAAY;gDACzC,YAAY;;;;;;;;;;;;;;;;;;;;;;;sCAOpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;oCAAa,UAAU;8CAAW;;;;;;8CAGnF,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,0BACC;;0DACE,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;qEAInD;;0DACE,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD;GAxPgB;;QAWV,iKAAA,CAAA,UAAO;;;KAXG", "debugId": null}}, {"offset": {"line": 3134, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,8KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,8KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,8KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,8KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,8KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3286, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/common/DeleteConfirmDialog.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogFooter,\n  AlertDialogHeader,\n  AlertDialogTitle,\n} from '@/components/ui/alert-dialog';\nimport { Trash2, AlertTriangle } from 'lucide-react';\n\ninterface DeleteConfirmDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  onConfirm: () => void;\n  title?: string;\n  description?: string;\n  itemName?: string;\n  itemType?: string;\n  isLoading?: boolean;\n  destructive?: boolean;\n}\n\nexport function DeleteConfirmDialog({\n  open,\n  onOpenChange,\n  onConfirm,\n  title,\n  description,\n  itemName,\n  itemType = 'item',\n  isLoading = false,\n  destructive = true,\n}: DeleteConfirmDialogProps) {\n  const defaultTitle = title || `Delete ${itemType}`;\n  const defaultDescription = description || \n    `Are you sure you want to delete ${itemName ? `\"${itemName}\"` : `this ${itemType}`}? This action cannot be undone.`;\n\n  return (\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\n      <AlertDialogContent>\n        <AlertDialogHeader>\n          <div className=\"flex items-center space-x-2\">\n            {destructive ? (\n              <AlertTriangle className=\"h-5 w-5 text-red-600\" />\n            ) : (\n              <Trash2 className=\"h-5 w-5 text-gray-600\" />\n            )}\n            <AlertDialogTitle>{defaultTitle}</AlertDialogTitle>\n          </div>\n          <AlertDialogDescription className=\"text-left\">\n            {defaultDescription}\n          </AlertDialogDescription>\n        </AlertDialogHeader>\n        <AlertDialogFooter>\n          <AlertDialogCancel disabled={isLoading}>\n            Cancel\n          </AlertDialogCancel>\n          <AlertDialogAction\n            onClick={onConfirm}\n            disabled={isLoading}\n            className={destructive ? 'bg-red-600 hover:bg-red-700' : ''}\n          >\n            {isLoading ? 'Deleting...' : 'Delete'}\n          </AlertDialogAction>\n        </AlertDialogFooter>\n      </AlertDialogContent>\n    </AlertDialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAUA;AAAA;AAZA;;;;AA0BO,SAAS,oBAAoB,EAClC,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,KAAK,EACL,WAAW,EACX,QAAQ,EACR,WAAW,MAAM,EACjB,YAAY,KAAK,EACjB,cAAc,IAAI,EACO;IACzB,MAAM,eAAe,SAAS,CAAC,OAAO,EAAE,UAAU;IAClD,MAAM,qBAAqB,eACzB,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,+BAA+B,CAAC;IAErH,qBACE,6LAAC,8IAAA,CAAA,cAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;8BACjB,6LAAC,8IAAA,CAAA,oBAAiB;;sCAChB,6LAAC;4BAAI,WAAU;;gCACZ,4BACC,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;yDAEzB,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAEpB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAErB,6LAAC,8IAAA,CAAA,yBAAsB;4BAAC,WAAU;sCAC/B;;;;;;;;;;;;8BAGL,6LAAC,8IAAA,CAAA,oBAAiB;;sCAChB,6LAAC,8IAAA,CAAA,oBAAiB;4BAAC,UAAU;sCAAW;;;;;;sCAGxC,6LAAC,8IAAA,CAAA,oBAAiB;4BAChB,SAAS;4BACT,UAAU;4BACV,WAAW,cAAc,gCAAgC;sCAExD,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAMzC;KA9CgB", "debugId": null}}, {"offset": {"line": 3400, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/api/base.ts"], "sourcesContent": ["import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';\nimport { getSession } from 'next-auth/react';\n\nconst baseQuery = fetchBaseQuery({\n  baseUrl: '/api',\n  prepareHeaders: async (headers) => {\n    const session = await getSession();\n    if (session?.accessToken) {\n      headers.set('authorization', `Bearer ${session.accessToken}`);\n    }\n    return headers;\n  },\n});\n\nexport const baseApi = createApi({\n  reducerPath: 'api',\n  baseQuery,\n  tagTypes: [\n    'User',\n    'Project',\n    'Terminology',\n    'Translation',\n    'Comment',\n    'File',\n    'Team',\n    'Organization',\n  ],\n  endpoints: () => ({}),\n});\n\nexport type ApiResponse<T> = {\n  data: T;\n  message?: string;\n  success: boolean;\n};\n\nexport type PaginatedResponse<T> = ApiResponse<{\n  items: T[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}>;\n\nexport type ApiError = {\n  error: string;\n  message?: string;\n  statusCode?: number;\n};\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEA,MAAM,YAAY,CAAA,GAAA,mLAAA,CAAA,iBAAc,AAAD,EAAE;IAC/B,SAAS;IACT,gBAAgB,OAAO;QACrB,MAAM,UAAU,MAAM,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;QAC/B,IAAI,SAAS,aAAa;YACxB,QAAQ,GAAG,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,WAAW,EAAE;QAC9D;QACA,OAAO;IACT;AACF;AAEO,MAAM,UAAU,CAAA,GAAA,qNAAA,CAAA,YAAS,AAAD,EAAE;IAC/B,aAAa;IACb;IACA,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW,IAAM,CAAC,CAAC,CAAC;AACtB", "debugId": null}}, {"offset": {"line": 3442, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/api/projects.ts"], "sourcesContent": ["import { baseApi, ApiResponse, PaginatedResponse } from './base';\n\nexport interface Project {\n  id: string;\n  name: string;\n  description: string;\n  status: 'draft' | 'in_progress' | 'review' | 'completed' | 'on_hold';\n  priority: 'low' | 'medium' | 'high' | 'urgent';\n  sourceLanguage: string;\n  targetLanguages: string[];\n  createdAt: string;\n  updatedAt: string;\n  dueDate?: string;\n  budget?: number;\n  spent?: number;\n  totalSegments: number;\n  completedSegments: number;\n  reviewedSegments: number;\n  approvedSegments: number;\n  teamMembers: ProjectMember[];\n  files: ProjectFile[];\n  createdBy: string;\n  organizationId: string;\n}\n\nexport interface ProjectMember {\n  id: string;\n  userId: string;\n  projectId: string;\n  role: 'translator' | 'reviewer' | 'project_manager';\n  languages: string[];\n  assignedAt: string;\n  user: {\n    id: string;\n    name: string;\n    email: string;\n    avatar?: string;\n  };\n}\n\nexport interface ProjectFile {\n  id: string;\n  projectId: string;\n  name: string;\n  originalName: string;\n  size: number;\n  type: string;\n  url: string;\n  segments: number;\n  uploadedAt: string;\n  uploadedBy: string;\n}\n\nexport interface CreateProjectRequest {\n  name: string;\n  description?: string;\n  sourceLanguage: string;\n  targetLanguages: string[];\n  dueDate?: string;\n  budget?: number;\n  priority: Project['priority'];\n  teamMembers?: {\n    userId: string;\n    role: ProjectMember['role'];\n    languages: string[];\n  }[];\n}\n\nexport interface UpdateProjectRequest extends Partial<CreateProjectRequest> {\n  status?: Project['status'];\n}\n\nexport interface ProjectFilters {\n  status?: Project['status'];\n  priority?: Project['priority'];\n  sourceLanguage?: string;\n  targetLanguage?: string;\n  createdBy?: string;\n  search?: string;\n  page?: number;\n  limit?: number;\n}\n\nexport interface ProjectStats {\n  totalProjects: number;\n  activeProjects: number;\n  completedProjects: number;\n  totalSegments: number;\n  completedSegments: number;\n  totalBudget: number;\n  spentBudget: number;\n}\n\nexport const projectsApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getProjects: builder.query<PaginatedResponse<Project>, ProjectFilters>({\n      query: (filters = {}) => {\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach(([key, value]) => {\n          if (value !== undefined && value !== null && value !== '') {\n            params.append(key, value.toString());\n          }\n        });\n        return {\n          url: `projects?${params.toString()}`,\n          method: 'GET',\n        };\n      },\n      providesTags: (result) =>\n        result?.data.items\n          ? [\n              ...result.data.items.map(({ id }) => ({ type: 'Project' as const, id })),\n              { type: 'Project', id: 'LIST' },\n            ]\n          : [{ type: 'Project', id: 'LIST' }],\n    }),\n\n    getProjectStats: builder.query<ApiResponse<ProjectStats>, void>({\n      query: () => ({\n        url: 'projects/stats',\n        method: 'GET',\n      }),\n      providesTags: [{ type: 'Project', id: 'STATS' }],\n    }),\n\n    createProject: builder.mutation<ApiResponse<Project>, CreateProjectRequest>({\n      query: (projectData) => ({\n        url: 'projects',\n        method: 'POST',\n        body: projectData,\n      }),\n      invalidatesTags: [\n        { type: 'Project', id: 'LIST' },\n        { type: 'Project', id: 'STATS' },\n      ],\n    }),\n\n    getProject: builder.query<ApiResponse<Project>, string>({\n      query: (id) => ({\n        url: `projects/${id}`,\n        method: 'GET',\n      }),\n      providesTags: (result, error, id) => [{ type: 'Project', id }],\n    }),\n\n    updateProject: builder.mutation<ApiResponse<Project>, { id: string; data: Partial<CreateProjectRequest> }>({\n      query: ({ id, data }) => ({\n        url: `projects/${id}`,\n        method: 'PATCH',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [\n        { type: 'Project', id },\n        { type: 'Project', id: 'LIST' },\n        { type: 'Project', id: 'STATS' },\n      ],\n    }),\n\n    deleteProject: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `projects/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (result, error, id) => [\n        { type: 'Project', id },\n        { type: 'Project', id: 'LIST' },\n        { type: 'Project', id: 'STATS' },\n      ],\n    }),\n\n    addProjectMember: builder.mutation<ApiResponse<ProjectMember>, {\n      projectId: string;\n      userId: string;\n      role: ProjectMember['role'];\n      languages: string[];\n    }>({\n      query: ({ projectId, ...data }) => ({\n        url: `/projects/${projectId}/members`,\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: projectId }],\n    }),\n\n    removeProjectMember: builder.mutation<ApiResponse<void>, {\n      projectId: string;\n      memberId: string;\n    }>({\n      query: ({ projectId, memberId }) => ({\n        url: `/projects/${projectId}/members/${memberId}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: projectId }],\n    }),\n\n    uploadProjectFile: builder.mutation<ApiResponse<ProjectFile>, {\n      projectId: string;\n      file: File;\n    }>({\n      query: ({ projectId, file }) => {\n        const formData = new FormData();\n        formData.append('file', file);\n        return {\n          url: `/projects/${projectId}/files`,\n          method: 'POST',\n          body: formData,\n        };\n      },\n      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: projectId }],\n    }),\n\n    deleteProjectFile: builder.mutation<ApiResponse<void>, {\n      projectId: string;\n      fileId: string;\n    }>({\n      query: ({ projectId, fileId }) => ({\n        url: `/projects/${projectId}/files/${fileId}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: projectId }],\n    }),\n\n    getProjectStats: builder.query<ApiResponse<{\n      totalProjects: number;\n      activeProjects: number;\n      completedProjects: number;\n      totalSegments: number;\n      completedSegments: number;\n      totalBudget: number;\n      spentBudget: number;\n    }>, void>({\n      query: () => '/projects/stats',\n      providesTags: ['Project'],\n    }),\n  }),\n});\n\nexport const {\n  useGetProjectsQuery,\n  useGetProjectStatsQuery,\n  useCreateProjectMutation,\n  useGetProjectQuery,\n  useUpdateProjectMutation,\n  useDeleteProjectMutation,\n} = projectsApi;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AA6FO,MAAM,cAAc,4HAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACjD,WAAW,CAAC,UAAY,CAAC;YACvB,aAAa,QAAQ,KAAK,CAA6C;gBACrE,OAAO,CAAC,UAAU,CAAC,CAAC;oBAClB,MAAM,SAAS,IAAI;oBACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;wBAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;4BACzD,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;wBACnC;oBACF;oBACA,OAAO;wBACL,KAAK,CAAC,SAAS,EAAE,OAAO,QAAQ,IAAI;wBACpC,QAAQ;oBACV;gBACF;gBACA,cAAc,CAAC,SACb,QAAQ,KAAK,QACT;2BACK,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAK,CAAC;gCAAE,MAAM;gCAAoB;4BAAG,CAAC;wBACtE;4BAAE,MAAM;4BAAW,IAAI;wBAAO;qBAC/B,GACD;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAO;qBAAE;YACzC;YAEA,iBAAiB,QAAQ,KAAK,CAAkC;gBAC9D,OAAO,IAAM,CAAC;wBACZ,KAAK;wBACL,QAAQ;oBACV,CAAC;gBACD,cAAc;oBAAC;wBAAE,MAAM;wBAAW,IAAI;oBAAQ;iBAAE;YAClD;YAEA,eAAe,QAAQ,QAAQ,CAA6C;gBAC1E,OAAO,CAAC,cAAgB,CAAC;wBACvB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBACf;wBAAE,MAAM;wBAAW,IAAI;oBAAO;oBAC9B;wBAAE,MAAM;wBAAW,IAAI;oBAAQ;iBAChC;YACH;YAEA,YAAY,QAAQ,KAAK,CAA+B;gBACtD,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,SAAS,EAAE,IAAI;wBACrB,QAAQ;oBACV,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,KAAO;wBAAC;4BAAE,MAAM;4BAAW;wBAAG;qBAAE;YAChE;YAEA,eAAe,QAAQ,QAAQ,CAA4E;gBACzG,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,SAAS,EAAE,IAAI;wBACrB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAW;wBAAG;wBACtB;4BAAE,MAAM;4BAAW,IAAI;wBAAO;wBAC9B;4BAAE,MAAM;4BAAW,IAAI;wBAAQ;qBAChC;YACH;YAEA,eAAe,QAAQ,QAAQ,CAA4B;gBACzD,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,SAAS,EAAE,IAAI;wBACrB,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,KAAO;wBACtC;4BAAE,MAAM;4BAAW;wBAAG;wBACtB;4BAAE,MAAM;4BAAW,IAAI;wBAAO;wBAC9B;4BAAE,MAAM;4BAAW,IAAI;wBAAQ;qBAChC;YACH;YAEA,kBAAkB,QAAQ,QAAQ,CAK/B;gBACD,OAAO,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,GAAK,CAAC;wBAClC,KAAK,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC;wBACrC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAU;qBAAE;YACzF;YAEA,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAK,CAAC;wBACnC,KAAK,CAAC,UAAU,EAAE,UAAU,SAAS,EAAE,UAAU;wBACjD,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAU;qBAAE;YACzF;YAEA,mBAAmB,QAAQ,QAAQ,CAGhC;gBACD,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;oBACzB,MAAM,WAAW,IAAI;oBACrB,SAAS,MAAM,CAAC,QAAQ;oBACxB,OAAO;wBACL,KAAK,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC;wBACnC,QAAQ;wBACR,MAAM;oBACR;gBACF;gBACA,iBAAiB,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAU;qBAAE;YACzF;YAEA,mBAAmB,QAAQ,QAAQ,CAGhC;gBACD,OAAO,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,GAAK,CAAC;wBACjC,KAAK,CAAC,UAAU,EAAE,UAAU,OAAO,EAAE,QAAQ;wBAC7C,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAU;qBAAE;YACzF;YAEA,iBAAiB,QAAQ,KAAK,CAQpB;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAU;YAC3B;QACF,CAAC;AACH;AAEO,MAAM,EACX,mBAAmB,EACnB,uBAAuB,EACvB,wBAAwB,EACxB,kBAAkB,EAClB,wBAAwB,EACxB,wBAAwB,EACzB,GAAG", "debugId": null}}, {"offset": {"line": 3638, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/app/dashboard/projects/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { DashboardLayout } from '@/components/layout/DashboardLayout';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport { Input } from '@/components/ui/input';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { CreateProjectDialog } from '@/components/projects/CreateProjectDialog';\nimport { DeleteConfirmDialog } from '@/components/common/DeleteConfirmDialog';\nimport {\n  useGetProjectsQuery,\n  useCreateProjectMutation,\n  useDeleteProjectMutation,\n  type Project,\n  type CreateProjectRequest,\n  type ProjectFilters\n} from '@/lib/api/projects';\nimport {\n  Plus,\n  Search,\n  MoreHorizontal,\n  Calendar,\n  Users,\n  Globe,\n  Clock,\n  AlertCircle,\n  RefreshCw\n} from 'lucide-react';\nimport { format } from 'date-fns';\n\n// Status configuration\nconst statusConfig = {\n  draft: { label: 'Draft', color: 'bg-gray-100 text-gray-800' },\n  in_progress: { label: 'In Progress', color: 'bg-blue-100 text-blue-800' },\n  review: { label: 'In Review', color: 'bg-yellow-100 text-yellow-800' },\n  completed: { label: 'Completed', color: 'bg-green-100 text-green-800' },\n  on_hold: { label: 'On Hold', color: 'bg-red-100 text-red-800' },\n};\n\nexport default function ProjectsPage() {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState<string>('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [showCreateDialog, setShowCreateDialog] = useState(false);\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);\n\n  // Build filters for API query\n  const filters: ProjectFilters = {\n    page: currentPage,\n    limit: 10,\n    ...(searchQuery && { search: searchQuery }),\n    ...(selectedStatus !== 'all' && {\n      status: selectedStatus as 'draft' | 'in_progress' | 'review' | 'completed' | 'on_hold'\n    }),\n  };\n\n  // API queries\n  const {\n    data: projectsResponse,\n    isLoading: isLoadingProjects,\n    isError: isProjectsError,\n    refetch: refetchProjects,\n  } = useGetProjectsQuery(filters, {\n    refetchOnFocus: true,\n    refetchOnMountOrArgChange: true,\n  });\n\n  // Mutations\n  const [createProject] = useCreateProjectMutation();\n  const [deleteProject, { isLoading: isDeleting }] = useDeleteProjectMutation();\n\n  const projects = projectsResponse?.data?.items || [];\n  const totalProjects = projectsResponse?.data?.total || 0;\n\n  const handleCreateProject = async (projectData: CreateProjectRequest) => {\n    try {\n      await createProject(projectData).unwrap();\n      setShowCreateDialog(false);\n    } catch (error) {\n      console.error('Failed to create project:', error);\n    }\n  };\n\n  const handleDeleteProject = async () => {\n    if (!projectToDelete) return;\n\n    try {\n      await deleteProject(projectToDelete.id).unwrap();\n      setShowDeleteDialog(false);\n      setProjectToDelete(null);\n    } catch (error) {\n      console.error('Failed to delete project:', error);\n    }\n  };\n\n  const openDeleteDialog = (project: Project) => {\n    setProjectToDelete(project);\n    setShowDeleteDialog(true);\n  };\n\n  const getStatusConfig = (status: string) => {\n    return statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;\n  };\n\n  const calculateProgress = (project: Project) => {\n    if (project.totalSegments === 0) return 0;\n    return Math.round((project.completedSegments / project.totalSegments) * 100);\n  };\n\n  const formatDate = (dateString: string) => {\n    try {\n      return format(new Date(dateString), 'MMM dd, yyyy');\n    } catch {\n      return dateString;\n    }\n  };\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Projects</h1>\n            <p className=\"text-gray-600\">\n              Manage your translation projects\n              {totalProjects > 0 && (\n                <span className=\"ml-2 text-sm text-gray-500\">\n                  ({totalProjects} total)\n                </span>\n              )}\n            </p>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => refetchProjects()}\n              disabled={isLoadingProjects}\n            >\n              <RefreshCw className={`mr-2 h-4 w-4 ${isLoadingProjects ? 'animate-spin' : ''}`} />\n              Refresh\n            </Button>\n            <Button onClick={() => setShowCreateDialog(true)}>\n              <Plus className=\"mr-2 h-4 w-4\" />\n              New Project\n            </Button>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <div className=\"relative flex-1\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <Input\n                  placeholder=\"Search projects...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n              <div className=\"flex gap-2\">\n                <Button\n                  variant={selectedStatus === 'all' ? 'default' : 'outline'}\n                  size=\"sm\"\n                  onClick={() => setSelectedStatus('all')}\n                >\n                  All\n                </Button>\n                <Button\n                  variant={selectedStatus === 'in_progress' ? 'default' : 'outline'}\n                  size=\"sm\"\n                  onClick={() => setSelectedStatus('in_progress')}\n                >\n                  Active\n                </Button>\n                <Button\n                  variant={selectedStatus === 'review' ? 'default' : 'outline'}\n                  size=\"sm\"\n                  onClick={() => setSelectedStatus('review')}\n                >\n                  Review\n                </Button>\n                <Button\n                  variant={selectedStatus === 'completed' ? 'default' : 'outline'}\n                  size=\"sm\"\n                  onClick={() => setSelectedStatus('completed')}\n                >\n                  Completed\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Error State */}\n        {isProjectsError && (\n          <Alert>\n            <AlertCircle className=\"h-4 w-4\" />\n            <AlertDescription>\n              Failed to load projects. Please try again.\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"ml-2\"\n                onClick={() => refetchProjects()}\n              >\n                Retry\n              </Button>\n            </AlertDescription>\n          </Alert>\n        )}\n\n        {/* Loading State */}\n        {isLoadingProjects && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {Array.from({ length: 4 }).map((_, i) => (\n              <Card key={i}>\n                <CardHeader>\n                  <Skeleton className=\"h-6 w-3/4\" />\n                  <Skeleton className=\"h-4 w-full mt-2\" />\n                </CardHeader>\n                <CardContent>\n                  <Skeleton className=\"h-4 w-1/2 mb-4\" />\n                  <Skeleton className=\"h-2 w-full mb-2\" />\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <Skeleton className=\"h-4 w-full\" />\n                    <Skeleton className=\"h-4 w-full\" />\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        )}\n\n        {/* Projects Grid */}\n        {!isLoadingProjects && !isProjectsError && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {projects.map((project) => {\n              const statusInfo = getStatusConfig(project.status);\n              const progress = calculateProgress(project);\n\n              return (\n                <Card key={project.id} className=\"hover:shadow-md transition-shadow cursor-pointer\">\n                  <CardHeader>\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <CardTitle className=\"text-lg\">{project.name}</CardTitle>\n                        <CardDescription className=\"mt-1\">\n                          {project.description}\n                        </CardDescription>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <Badge className={statusInfo.color}>\n                          {statusInfo.label}\n                        </Badge>\n                        <Button\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            openDeleteDialog(project);\n                          }}\n                        >\n                          <MoreHorizontal className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </div>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-4\">\n                      {/* Progress */}\n                      <div>\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <span className=\"text-sm font-medium\">Progress</span>\n                          <span className=\"text-sm text-gray-600\">{progress}%</span>\n                        </div>\n                        <Progress value={progress} />\n                        <div className=\"flex items-center justify-between mt-1 text-xs text-gray-500\">\n                          <span>{project.completedSegments} completed</span>\n                          <span>{project.totalSegments} total segments</span>\n                        </div>\n                      </div>\n\n                      {/* Project Details */}\n                      <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                        <div className=\"flex items-center text-gray-600\">\n                          <Globe className=\"mr-2 h-4 w-4\" />\n                          <span>{project.sourceLanguage} → {project.targetLanguages.join(', ')}</span>\n                        </div>\n                        <div className=\"flex items-center text-gray-600\">\n                          <Users className=\"mr-2 h-4 w-4\" />\n                          <span>{project.teamMembers.length} members</span>\n                        </div>\n                        <div className=\"flex items-center text-gray-600\">\n                          <Calendar className=\"mr-2 h-4 w-4\" />\n                          <span>\n                            {project.dueDate ? `Due ${formatDate(project.dueDate)}` : 'No deadline'}\n                          </span>\n                        </div>\n                        <div className=\"flex items-center text-gray-600\">\n                          <Clock className=\"mr-2 h-4 w-4\" />\n                          <span>Created {formatDate(project.createdAt)}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              );\n            })}\n          </div>\n        )}\n\n        {/* Empty State */}\n        {!isLoadingProjects && !isProjectsError && projects.length === 0 && (\n          <Card>\n            <CardContent className=\"p-12 text-center\">\n              <div className=\"text-gray-400 mb-4\">\n                <Search className=\"h-12 w-12 mx-auto\" />\n              </div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No projects found</h3>\n              <p className=\"text-gray-600 mb-4\">\n                {searchQuery ? 'Try adjusting your search criteria' : 'Get started by creating your first project'}\n              </p>\n              <Button onClick={() => setShowCreateDialog(true)}>\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Create Project\n              </Button>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Modals */}\n        <CreateProjectDialog\n          open={showCreateDialog}\n          onOpenChange={setShowCreateDialog}\n          onSubmit={handleCreateProject}\n        />\n\n        <DeleteConfirmDialog\n          open={showDeleteDialog}\n          onOpenChange={setShowDeleteDialog}\n          onConfirm={handleDeleteProject}\n          title=\"Delete Project\"\n          description=\"Are you sure you want to delete this project? This action cannot be undone and will remove all associated data.\"\n          itemName={projectToDelete?.name}\n          itemType=\"project\"\n          isLoading={isDeleting}\n        />\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAhCA;;;;;;;;;;;;;;;AAkCA,uBAAuB;AACvB,MAAM,eAAe;IACnB,OAAO;QAAE,OAAO;QAAS,OAAO;IAA4B;IAC5D,aAAa;QAAE,OAAO;QAAe,OAAO;IAA4B;IACxE,QAAQ;QAAE,OAAO;QAAa,OAAO;IAAgC;IACrE,WAAW;QAAE,OAAO;QAAa,OAAO;IAA8B;IACtE,SAAS;QAAE,OAAO;QAAW,OAAO;IAA0B;AAChE;AAEe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAEvE,8BAA8B;IAC9B,MAAM,UAA0B;QAC9B,MAAM;QACN,OAAO;QACP,GAAI,eAAe;YAAE,QAAQ;QAAY,CAAC;QAC1C,GAAI,mBAAmB,SAAS;YAC9B,QAAQ;QACV,CAAC;IACH;IAEA,cAAc;IACd,MAAM,EACJ,MAAM,gBAAgB,EACtB,WAAW,iBAAiB,EAC5B,SAAS,eAAe,EACxB,SAAS,eAAe,EACzB,GAAG,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS;QAC/B,gBAAgB;QAChB,2BAA2B;IAC7B;IAEA,YAAY;IACZ,MAAM,CAAC,cAAc,GAAG,CAAA,GAAA,gIAAA,CAAA,2BAAwB,AAAD;IAC/C,MAAM,CAAC,eAAe,EAAE,WAAW,UAAU,EAAE,CAAC,GAAG,CAAA,GAAA,gIAAA,CAAA,2BAAwB,AAAD;IAE1E,MAAM,WAAW,kBAAkB,MAAM,SAAS,EAAE;IACpD,MAAM,gBAAgB,kBAAkB,MAAM,SAAS;IAEvD,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,cAAc,aAAa,MAAM;YACvC,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,MAAM,cAAc,gBAAgB,EAAE,EAAE,MAAM;YAC9C,oBAAoB;YACpB,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,YAAY,CAAC,OAAoC,IAAI,aAAa,KAAK;IAChF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,QAAQ,aAAa,KAAK,GAAG,OAAO;QACxC,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,iBAAiB,GAAG,QAAQ,aAAa,GAAI;IAC1E;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI;YACF,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa;QACtC,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,qBACE,6LAAC,kJAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;;wCAAgB;wCAE1B,gBAAgB,mBACf,6LAAC;4CAAK,WAAU;;gDAA6B;gDACzC;gDAAc;;;;;;;;;;;;;;;;;;;sCAKxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM;oCACf,UAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAW,CAAC,aAAa,EAAE,oBAAoB,iBAAiB,IAAI;;;;;;wCAAI;;;;;;;8CAGrF,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,oBAAoB;;sDACzC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;8BAOvC,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,mBAAmB,QAAQ,YAAY;4CAChD,MAAK;4CACL,SAAS,IAAM,kBAAkB;sDAClC;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,mBAAmB,gBAAgB,YAAY;4CACxD,MAAK;4CACL,SAAS,IAAM,kBAAkB;sDAClC;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,mBAAmB,WAAW,YAAY;4CACnD,MAAK;4CACL,SAAS,IAAM,kBAAkB;sDAClC;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,mBAAmB,cAAc,YAAY;4CACtD,MAAK;4CACL,SAAS,IAAM,kBAAkB;sDAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASR,iCACC,6LAAC,oIAAA,CAAA,QAAK;;sCACJ,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC,oIAAA,CAAA,mBAAgB;;gCAAC;8CAEhB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM;8CAChB;;;;;;;;;;;;;;;;;;gBAQN,mCACC,6LAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;2BAVf;;;;;;;;;;gBAmBhB,CAAC,qBAAqB,CAAC,iCACtB,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,aAAa,gBAAgB,QAAQ,MAAM;wBACjD,MAAM,WAAW,kBAAkB;wBAEnC,qBACE,6LAAC,mIAAA,CAAA,OAAI;4BAAkB,WAAU;;8CAC/B,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAW,QAAQ,IAAI;;;;;;kEAC5C,6LAAC,mIAAA,CAAA,kBAAe;wDAAC,WAAU;kEACxB,QAAQ,WAAW;;;;;;;;;;;;0DAGxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAW,WAAW,KAAK;kEAC/B,WAAW,KAAK;;;;;;kEAEnB,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,iBAAiB;wDACnB;kEAEA,cAAA,6LAAC,mNAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAKlC,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;0EACtC,6LAAC;gEAAK,WAAU;;oEAAyB;oEAAS;;;;;;;;;;;;;kEAEpD,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,OAAO;;;;;;kEACjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAM,QAAQ,iBAAiB;oEAAC;;;;;;;0EACjC,6LAAC;;oEAAM,QAAQ,aAAa;oEAAC;;;;;;;;;;;;;;;;;;;0DAKjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;;oEAAM,QAAQ,cAAc;oEAAC;oEAAI,QAAQ,eAAe,CAAC,IAAI,CAAC;;;;;;;;;;;;;kEAEjE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;;oEAAM,QAAQ,WAAW,CAAC,MAAM;oEAAC;;;;;;;;;;;;;kEAEpC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;0EACE,QAAQ,OAAO,GAAG,CAAC,IAAI,EAAE,WAAW,QAAQ,OAAO,GAAG,GAAG;;;;;;;;;;;;kEAG9D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;;oEAAK;oEAAS,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA3D1C,QAAQ,EAAE;;;;;oBAkEzB;;;;;;gBAKH,CAAC,qBAAqB,CAAC,mBAAmB,SAAS,MAAM,KAAK,mBAC7D,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CACV,cAAc,uCAAuC;;;;;;0CAExD,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,oBAAoB;;kDACzC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAQzC,6LAAC,wJAAA,CAAA,sBAAmB;oBAClB,MAAM;oBACN,cAAc;oBACd,UAAU;;;;;;8BAGZ,6LAAC,sJAAA,CAAA,sBAAmB;oBAClB,MAAM;oBACN,cAAc;oBACd,WAAW;oBACX,OAAM;oBACN,aAAY;oBACZ,UAAU,iBAAiB;oBAC3B,UAAS;oBACT,WAAW;;;;;;;;;;;;;;;;;AAKrB;GA5TwB;;QAwBlB,gIAAA,CAAA,sBAAmB;QAMC,gIAAA,CAAA,2BAAwB;QACG,gIAAA,CAAA,2BAAwB;;;KA/BrD", "debugId": null}}]}