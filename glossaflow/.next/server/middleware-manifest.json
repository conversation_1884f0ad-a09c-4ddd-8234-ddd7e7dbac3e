{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tAmvl4mg1oDWe5Dsprh8Ih7p7p0YuTcmWBNmQzoBO34=", "__NEXT_PREVIEW_MODE_ID": "85fb3fb3c19b5752914ba0cbfe3f8514", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2488325b25e180e420541de2c4c3330de84a05deff9241c7c0fa5768380c32c0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "58b7cb5feb95055fa1e8261c8d5b349778c8a8d1f275284c2df54b4fa7f347e3"}}}, "sortedMiddleware": ["/"], "functions": {}}