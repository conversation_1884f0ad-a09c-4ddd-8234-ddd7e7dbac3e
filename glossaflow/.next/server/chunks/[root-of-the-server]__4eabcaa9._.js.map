{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport GitHubProvider from 'next-auth/providers/github';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport { SupabaseAdapter } from '@next-auth/supabase-adapter';\nimport { createClient } from '@supabase/supabase-js';\n\nconst supabase = createClient(\n  process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n);\n\nexport const authOptions: NextAuthOptions = {\n  // Remove adapter temporarily to test without database integration\n  // adapter: SupabaseAdapter({\n  //   url: process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  //   secret: process.env.SUPABASE_SERVICE_ROLE_KEY!,\n  // }),\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_CLIENT_ID!,\n      clientSecret: process.env.GITHUB_CLIENT_SECRET!,\n    }),\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) return null;\n        \n        try {\n          const { data, error } = await supabase.auth.signInWithPassword({\n            email: credentials.email as string,\n            password: credentials.password as string,\n          });\n          \n          if (error || !data.user) return null;\n          \n          return {\n            id: data.user.id,\n            email: data.user.email!,\n            name: data.user.user_metadata?.name || data.user.email,\n            image: data.user.user_metadata?.avatar_url,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async signIn({ user, account, profile }) {\n      if (account?.provider === 'google' && profile?.email) {\n        try {\n          // Create Supabase client\n          const supabase = createClient(\n            process.env.NEXT_PUBLIC_SUPABASE_URL!,\n            process.env.SUPABASE_SERVICE_ROLE_KEY!\n          );\n\n          // Check if user exists\n          const { data: existingUser } = await supabase\n            .from('users')\n            .select('*')\n            .eq('email', profile.email)\n            .single();\n\n          if (!existingUser) {\n            // Create new user\n            const { error } = await supabase\n              .from('users')\n              .insert({\n                email: profile.email,\n                name: profile.name || user.name,\n                avatar_url: profile.picture || user.image,\n                email_verified: new Date().toISOString(),\n              });\n\n            if (error) {\n              console.error('Error creating user:', error);\n              return false;\n            }\n          }\n\n          return true;\n        } catch (error) {\n          console.error('Sign in error:', error);\n          return false;\n        }\n      }\n      return true;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        // Get user data from Supabase\n        try {\n          const supabase = createClient(\n            process.env.NEXT_PUBLIC_SUPABASE_URL!,\n            process.env.SUPABASE_SERVICE_ROLE_KEY!\n          );\n\n          const { data: userData } = await supabase\n            .from('users')\n            .select('*')\n            .eq('email', session.user.email)\n            .single();\n\n          if (userData) {\n            session.user.id = userData.id;\n            session.user.name = userData.name;\n            session.user.image = userData.avatar_url;\n          }\n        } catch (error) {\n          console.error('Session error:', error);\n        }\n      }\n      return session;\n    },\n    async jwt({ token, user, account, profile }) {\n      if (user) {\n        token.sub = user.id;\n      }\n      return token;\n    },\n    async signIn({ user, account, profile }) {\n      // Allow sign in\n      return true;\n    },\n    async redirect({ url, baseUrl }) {\n      // Allows relative callback URLs\n      if (url.startsWith('/')) return `${baseUrl}${url}`;\n      // Allows callback URLs on the same origin\n      else if (new URL(url).origin === baseUrl) return url;\n      return baseUrl;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n    error: '/auth/error',\n    verifyRequest: '/auth/verify-request',\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AAEA;;;;;AAEA,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;AAGhC,MAAM,cAA+B;IAC1C,kEAAkE;IAClE,6BAA6B;IAC7B,gDAAgD;IAChD,oDAAoD;IACpD,MAAM;IACN,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU,OAAO;gBAE1D,IAAI;oBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;wBAC7D,OAAO,YAAY,KAAK;wBACxB,UAAU,YAAY,QAAQ;oBAChC;oBAEA,IAAI,SAAS,CAAC,KAAK,IAAI,EAAE,OAAO;oBAEhC,OAAO;wBACL,IAAI,KAAK,IAAI,CAAC,EAAE;wBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;wBACtB,MAAM,KAAK,IAAI,CAAC,aAAa,EAAE,QAAQ,KAAK,IAAI,CAAC,KAAK;wBACtD,OAAO,KAAK,IAAI,CAAC,aAAa,EAAE;oBAClC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,IAAI,SAAS,aAAa,YAAY,SAAS,OAAO;gBACpD,IAAI;oBACF,yBAAyB;oBACzB,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;oBAGvC,uBAAuB;oBACvB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,QAAQ,KAAK,EACzB,MAAM;oBAET,IAAI,CAAC,cAAc;wBACjB,kBAAkB;wBAClB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,CAAC;4BACN,OAAO,QAAQ,KAAK;4BACpB,MAAM,QAAQ,IAAI,IAAI,KAAK,IAAI;4BAC/B,YAAY,QAAQ,OAAO,IAAI,KAAK,KAAK;4BACzC,gBAAgB,IAAI,OAAO,WAAW;wBACxC;wBAEF,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,wBAAwB;4BACtC,OAAO;wBACT;oBACF;oBAEA,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kBAAkB;oBAChC,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,8BAA8B;gBAC9B,IAAI;oBACF,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;oBAGvC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,QAAQ,IAAI,CAAC,KAAK,EAC9B,MAAM;oBAET,IAAI,UAAU;wBACZ,QAAQ,IAAI,CAAC,EAAE,GAAG,SAAS,EAAE;wBAC7B,QAAQ,IAAI,CAAC,IAAI,GAAG,SAAS,IAAI;wBACjC,QAAQ,IAAI,CAAC,KAAK,GAAG,SAAS,UAAU;oBAC1C;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAClC;YACF;YACA,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACzC,IAAI,MAAM;gBACR,MAAM,GAAG,GAAG,KAAK,EAAE;YACrB;YACA,OAAO;QACT;QACA,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,gBAAgB;YAChB,OAAO;QACT;QACA,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAE;YAC7B,gCAAgC;YAChC,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;iBAE7C,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YACjD,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,eAAe;IACjB;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/app/api/dashboard/activity/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport { createClient } from '@supabase/supabase-js';\n\nconst supabase = createClient(\n  process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n);\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    \n    if (!session?.user) {\n      return NextResponse.json(\n        { error: 'Unauthorized', success: false },\n        { status: 401 }\n      );\n    }\n\n    const { searchParams } = new URL(request.url);\n    const limit = parseInt(searchParams.get('limit') || '10');\n\n    // Get organization ID from session or use a default for now\n    const organizationId = session.user.organizationId;\n\n    // If no organization ID, return empty activity\n    if (!organizationId) {\n      return NextResponse.json({\n        success: true,\n        data: {\n          activities: [],\n          total: 0,\n        },\n      });\n    }\n\n    // Get user information for activity attribution\n    const { data: users } = await supabase.auth.admin.listUsers();\n    const userMap = new Map();\n    users?.users?.forEach(user => {\n      userMap.set(user.id, {\n        id: user.id,\n        name: user.user_metadata?.name || user.user_metadata?.full_name || 'Unknown User',\n        email: user.email,\n        avatar: user.user_metadata?.avatar_url || user.user_metadata?.picture,\n      });\n    });\n\n    // Fetch recent project activities\n    const { data: recentProjects, error: projectsError } = await supabase\n      .from('projects')\n      .select('id, name, status, created_at, updated_at, created_by')\n      .eq('organization_id', organizationId)\n      .order('updated_at', { ascending: false })\n      .limit(limit);\n\n    if (projectsError) {\n      console.error('Recent projects fetch error:', projectsError);\n    }\n\n    // Fetch recent terminology activities (handle missing table)\n    let recentTerms = null;\n    let termsError = null;\n\n    try {\n      const result = await supabase\n        .from('terminology')\n        .select('id, term, status, created_at, updated_at, created_by')\n        .eq('organization_id', organizationId)\n        .order('updated_at', { ascending: false })\n        .limit(limit);\n      recentTerms = result.data;\n      termsError = result.error;\n    } catch (error) {\n      console.error('Terminology table does not exist:', error);\n      termsError = error;\n    }\n\n    if (termsError) {\n      console.error('Recent terms fetch error:', termsError);\n    }\n\n    // Fetch recent team activities\n    const { data: recentTeamMembers, error: teamError } = await supabase\n      .from('team_members')\n      .select('id, role_name, status, created_at, updated_at, created_by, user_id')\n      .eq('organization_id', organizationId)\n      .order('updated_at', { ascending: false })\n      .limit(limit);\n\n    if (teamError) {\n      console.error('Recent team members fetch error:', teamError);\n    }\n\n    // Fetch recent invitations\n    const { data: recentInvitations, error: invitationsError } = await supabase\n      .from('team_invitations')\n      .select('id, email, role_name, status, created_at, updated_at, invited_by')\n      .eq('organization_id', organizationId)\n      .order('created_at', { ascending: false })\n      .limit(limit);\n\n    if (invitationsError) {\n      console.error('Recent invitations fetch error:', invitationsError);\n    }\n\n    // Create activity feed items\n    const activities = [];\n\n    // Add project activities\n    recentProjects?.forEach(project => {\n      const user = userMap.get(project.created_by);\n      const isNew = new Date(project.created_at).getTime() === new Date(project.updated_at).getTime();\n      \n      activities.push({\n        id: `project-${project.id}`,\n        type: 'project',\n        action: isNew ? 'created' : 'updated',\n        title: project.name,\n        description: isNew \n          ? `${user?.name || 'Someone'} created project \"${project.name}\"`\n          : `Project \"${project.name}\" status changed to ${project.status}`,\n        user: user || { name: 'Unknown User', email: '', avatar: null },\n        timestamp: project.updated_at,\n        metadata: {\n          projectId: project.id,\n          status: project.status,\n        },\n      });\n    });\n\n    // Add terminology activities\n    recentTerms?.forEach(term => {\n      const user = userMap.get(term.created_by);\n      const isNew = new Date(term.created_at).getTime() === new Date(term.updated_at).getTime();\n      \n      activities.push({\n        id: `term-${term.id}`,\n        type: 'terminology',\n        action: isNew ? 'created' : 'updated',\n        title: term.term,\n        description: isNew \n          ? `${user?.name || 'Someone'} added term \"${term.term}\"`\n          : `Term \"${term.term}\" status changed to ${term.status}`,\n        user: user || { name: 'Unknown User', email: '', avatar: null },\n        timestamp: term.updated_at,\n        metadata: {\n          termId: term.id,\n          status: term.status,\n        },\n      });\n    });\n\n    // Add team member activities\n    recentTeamMembers?.forEach(member => {\n      const user = userMap.get(member.created_by);\n      const memberUser = userMap.get(member.user_id);\n      const isNew = new Date(member.created_at).getTime() === new Date(member.updated_at).getTime();\n      \n      activities.push({\n        id: `team-${member.id}`,\n        type: 'team',\n        action: isNew ? 'joined' : 'updated',\n        title: `${memberUser?.name || 'Team Member'}`,\n        description: isNew \n          ? `${memberUser?.name || 'Someone'} joined as ${member.role_name}`\n          : `${memberUser?.name || 'Team member'} status changed to ${member.status}`,\n        user: memberUser || { name: 'Unknown User', email: '', avatar: null },\n        timestamp: member.updated_at,\n        metadata: {\n          memberId: member.id,\n          role: member.role_name,\n          status: member.status,\n        },\n      });\n    });\n\n    // Add invitation activities\n    recentInvitations?.forEach(invitation => {\n      const user = userMap.get(invitation.invited_by);\n      \n      activities.push({\n        id: `invitation-${invitation.id}`,\n        type: 'invitation',\n        action: invitation.status === 'pending' ? 'invited' : invitation.status,\n        title: invitation.email,\n        description: invitation.status === 'pending'\n          ? `${user?.name || 'Someone'} invited ${invitation.email} as ${invitation.role_name}`\n          : `Invitation to ${invitation.email} was ${invitation.status}`,\n        user: user || { name: 'Unknown User', email: '', avatar: null },\n        timestamp: invitation.updated_at || invitation.created_at,\n        metadata: {\n          invitationId: invitation.id,\n          email: invitation.email,\n          role: invitation.role_name,\n          status: invitation.status,\n        },\n      });\n    });\n\n    // Sort activities by timestamp (most recent first)\n    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n\n    // Limit to requested number of activities\n    const limitedActivities = activities.slice(0, limit);\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        activities: limitedActivities,\n        total: activities.length,\n      },\n    });\n  } catch (error) {\n    console.error('Dashboard activity API error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error', success: false },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;AAGhC,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAgB,SAAS;YAAM,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QAEpD,4DAA4D;QAC5D,MAAM,iBAAiB,QAAQ,IAAI,CAAC,cAAc;QAElD,+CAA+C;QAC/C,IAAI,CAAC,gBAAgB;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ,YAAY,EAAE;oBACd,OAAO;gBACT;YACF;QACF;QAEA,gDAAgD;QAChD,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,SAAS;QAC3D,MAAM,UAAU,IAAI;QACpB,OAAO,OAAO,QAAQ,CAAA;YACpB,QAAQ,GAAG,CAAC,KAAK,EAAE,EAAE;gBACnB,IAAI,KAAK,EAAE;gBACX,MAAM,KAAK,aAAa,EAAE,QAAQ,KAAK,aAAa,EAAE,aAAa;gBACnE,OAAO,KAAK,KAAK;gBACjB,QAAQ,KAAK,aAAa,EAAE,cAAc,KAAK,aAAa,EAAE;YAChE;QACF;QAEA,kCAAkC;QAClC,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,YACL,MAAM,CAAC,wDACP,EAAE,CAAC,mBAAmB,gBACtB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC;QAET,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,gCAAgC;QAChD;QAEA,6DAA6D;QAC7D,IAAI,cAAc;QAClB,IAAI,aAAa;QAEjB,IAAI;YACF,MAAM,SAAS,MAAM,SAClB,IAAI,CAAC,eACL,MAAM,CAAC,wDACP,EAAE,CAAC,mBAAmB,gBACtB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM,GACvC,KAAK,CAAC;YACT,cAAc,OAAO,IAAI;YACzB,aAAa,OAAO,KAAK;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,aAAa;QACf;QAEA,IAAI,YAAY;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;QAEA,+BAA+B;QAC/B,MAAM,EAAE,MAAM,iBAAiB,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACzD,IAAI,CAAC,gBACL,MAAM,CAAC,sEACP,EAAE,CAAC,mBAAmB,gBACtB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC;QAET,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,oCAAoC;QACpD;QAEA,2BAA2B;QAC3B,MAAM,EAAE,MAAM,iBAAiB,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,SAChE,IAAI,CAAC,oBACL,MAAM,CAAC,oEACP,EAAE,CAAC,mBAAmB,gBACtB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC;QAET,IAAI,kBAAkB;YACpB,QAAQ,KAAK,CAAC,mCAAmC;QACnD;QAEA,6BAA6B;QAC7B,MAAM,aAAa,EAAE;QAErB,yBAAyB;QACzB,gBAAgB,QAAQ,CAAA;YACtB,MAAM,OAAO,QAAQ,GAAG,CAAC,QAAQ,UAAU;YAC3C,MAAM,QAAQ,IAAI,KAAK,QAAQ,UAAU,EAAE,OAAO,OAAO,IAAI,KAAK,QAAQ,UAAU,EAAE,OAAO;YAE7F,WAAW,IAAI,CAAC;gBACd,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;gBAC3B,MAAM;gBACN,QAAQ,QAAQ,YAAY;gBAC5B,OAAO,QAAQ,IAAI;gBACnB,aAAa,QACT,GAAG,MAAM,QAAQ,UAAU,kBAAkB,EAAE,QAAQ,IAAI,CAAC,CAAC,CAAC,GAC9D,CAAC,SAAS,EAAE,QAAQ,IAAI,CAAC,oBAAoB,EAAE,QAAQ,MAAM,EAAE;gBACnE,MAAM,QAAQ;oBAAE,MAAM;oBAAgB,OAAO;oBAAI,QAAQ;gBAAK;gBAC9D,WAAW,QAAQ,UAAU;gBAC7B,UAAU;oBACR,WAAW,QAAQ,EAAE;oBACrB,QAAQ,QAAQ,MAAM;gBACxB;YACF;QACF;QAEA,6BAA6B;QAC7B,aAAa,QAAQ,CAAA;YACnB,MAAM,OAAO,QAAQ,GAAG,CAAC,KAAK,UAAU;YACxC,MAAM,QAAQ,IAAI,KAAK,KAAK,UAAU,EAAE,OAAO,OAAO,IAAI,KAAK,KAAK,UAAU,EAAE,OAAO;YAEvF,WAAW,IAAI,CAAC;gBACd,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACrB,MAAM;gBACN,QAAQ,QAAQ,YAAY;gBAC5B,OAAO,KAAK,IAAI;gBAChB,aAAa,QACT,GAAG,MAAM,QAAQ,UAAU,aAAa,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,GACtD,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,oBAAoB,EAAE,KAAK,MAAM,EAAE;gBAC1D,MAAM,QAAQ;oBAAE,MAAM;oBAAgB,OAAO;oBAAI,QAAQ;gBAAK;gBAC9D,WAAW,KAAK,UAAU;gBAC1B,UAAU;oBACR,QAAQ,KAAK,EAAE;oBACf,QAAQ,KAAK,MAAM;gBACrB;YACF;QACF;QAEA,6BAA6B;QAC7B,mBAAmB,QAAQ,CAAA;YACzB,MAAM,OAAO,QAAQ,GAAG,CAAC,OAAO,UAAU;YAC1C,MAAM,aAAa,QAAQ,GAAG,CAAC,OAAO,OAAO;YAC7C,MAAM,QAAQ,IAAI,KAAK,OAAO,UAAU,EAAE,OAAO,OAAO,IAAI,KAAK,OAAO,UAAU,EAAE,OAAO;YAE3F,WAAW,IAAI,CAAC;gBACd,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;gBACvB,MAAM;gBACN,QAAQ,QAAQ,WAAW;gBAC3B,OAAO,GAAG,YAAY,QAAQ,eAAe;gBAC7C,aAAa,QACT,GAAG,YAAY,QAAQ,UAAU,WAAW,EAAE,OAAO,SAAS,EAAE,GAChE,GAAG,YAAY,QAAQ,cAAc,mBAAmB,EAAE,OAAO,MAAM,EAAE;gBAC7E,MAAM,cAAc;oBAAE,MAAM;oBAAgB,OAAO;oBAAI,QAAQ;gBAAK;gBACpE,WAAW,OAAO,UAAU;gBAC5B,UAAU;oBACR,UAAU,OAAO,EAAE;oBACnB,MAAM,OAAO,SAAS;oBACtB,QAAQ,OAAO,MAAM;gBACvB;YACF;QACF;QAEA,4BAA4B;QAC5B,mBAAmB,QAAQ,CAAA;YACzB,MAAM,OAAO,QAAQ,GAAG,CAAC,WAAW,UAAU;YAE9C,WAAW,IAAI,CAAC;gBACd,IAAI,CAAC,WAAW,EAAE,WAAW,EAAE,EAAE;gBACjC,MAAM;gBACN,QAAQ,WAAW,MAAM,KAAK,YAAY,YAAY,WAAW,MAAM;gBACvE,OAAO,WAAW,KAAK;gBACvB,aAAa,WAAW,MAAM,KAAK,YAC/B,GAAG,MAAM,QAAQ,UAAU,SAAS,EAAE,WAAW,KAAK,CAAC,IAAI,EAAE,WAAW,SAAS,EAAE,GACnF,CAAC,cAAc,EAAE,WAAW,KAAK,CAAC,KAAK,EAAE,WAAW,MAAM,EAAE;gBAChE,MAAM,QAAQ;oBAAE,MAAM;oBAAgB,OAAO;oBAAI,QAAQ;gBAAK;gBAC9D,WAAW,WAAW,UAAU,IAAI,WAAW,UAAU;gBACzD,UAAU;oBACR,cAAc,WAAW,EAAE;oBAC3B,OAAO,WAAW,KAAK;oBACvB,MAAM,WAAW,SAAS;oBAC1B,QAAQ,WAAW,MAAM;gBAC3B;YACF;QACF;QAEA,mDAAmD;QACnD,WAAW,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAEzF,0CAA0C;QAC1C,MAAM,oBAAoB,WAAW,KAAK,CAAC,GAAG;QAE9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,YAAY;gBACZ,OAAO,WAAW,MAAM;YAC1B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAyB,SAAS;QAAM,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}