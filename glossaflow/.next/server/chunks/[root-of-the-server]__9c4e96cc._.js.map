{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport GitHubProvider from 'next-auth/providers/github';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport { SupabaseAdapter } from '@next-auth/supabase-adapter';\nimport { createClient } from '@supabase/supabase-js';\n\nconst supabase = createClient(\n  process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n);\n\nexport const authOptions: NextAuthOptions = {\n  // Remove adapter temporarily to test without database integration\n  // adapter: SupabaseAdapter({\n  //   url: process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  //   secret: process.env.SUPABASE_SERVICE_ROLE_KEY!,\n  // }),\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_CLIENT_ID!,\n      clientSecret: process.env.GITHUB_CLIENT_SECRET!,\n    }),\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) return null;\n        \n        try {\n          const { data, error } = await supabase.auth.signInWithPassword({\n            email: credentials.email as string,\n            password: credentials.password as string,\n          });\n          \n          if (error || !data.user) return null;\n          \n          return {\n            id: data.user.id,\n            email: data.user.email!,\n            name: data.user.user_metadata?.name || data.user.email,\n            image: data.user.user_metadata?.avatar_url,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async signIn({ user, account, profile }) {\n      if (account?.provider === 'google' && profile?.email) {\n        try {\n          // Create Supabase client\n          const supabase = createClient(\n            process.env.NEXT_PUBLIC_SUPABASE_URL!,\n            process.env.SUPABASE_SERVICE_ROLE_KEY!\n          );\n\n          // Check if user exists\n          const { data: existingUser } = await supabase\n            .from('users')\n            .select('*')\n            .eq('email', profile.email)\n            .single();\n\n          if (!existingUser) {\n            // Create new user\n            const { error } = await supabase\n              .from('users')\n              .insert({\n                email: profile.email,\n                name: profile.name || user.name,\n                avatar_url: profile.picture || user.image,\n                email_verified: new Date().toISOString(),\n              });\n\n            if (error) {\n              console.error('Error creating user:', error);\n              return false;\n            }\n          }\n\n          return true;\n        } catch (error) {\n          console.error('Sign in error:', error);\n          return false;\n        }\n      }\n      return true;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        // Get user data from Supabase\n        try {\n          const supabase = createClient(\n            process.env.NEXT_PUBLIC_SUPABASE_URL!,\n            process.env.SUPABASE_SERVICE_ROLE_KEY!\n          );\n\n          const { data: userData } = await supabase\n            .from('users')\n            .select('*')\n            .eq('email', session.user.email)\n            .single();\n\n          if (userData) {\n            session.user.id = userData.id;\n            session.user.name = userData.name;\n            session.user.image = userData.avatar_url;\n          }\n        } catch (error) {\n          console.error('Session error:', error);\n        }\n      }\n      return session;\n    },\n    async jwt({ token, user, account, profile }) {\n      if (user) {\n        token.sub = user.id;\n      }\n      return token;\n    },\n    async signIn({ user, account, profile }) {\n      // Allow sign in\n      return true;\n    },\n    async redirect({ url, baseUrl }) {\n      // Allows relative callback URLs\n      if (url.startsWith('/')) return `${baseUrl}${url}`;\n      // Allows callback URLs on the same origin\n      else if (new URL(url).origin === baseUrl) return url;\n      return baseUrl;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n    error: '/auth/error',\n    verifyRequest: '/auth/verify-request',\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AAEA;;;;;AAEA,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;AAGhC,MAAM,cAA+B;IAC1C,kEAAkE;IAClE,6BAA6B;IAC7B,gDAAgD;IAChD,oDAAoD;IACpD,MAAM;IACN,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU,OAAO;gBAE1D,IAAI;oBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;wBAC7D,OAAO,YAAY,KAAK;wBACxB,UAAU,YAAY,QAAQ;oBAChC;oBAEA,IAAI,SAAS,CAAC,KAAK,IAAI,EAAE,OAAO;oBAEhC,OAAO;wBACL,IAAI,KAAK,IAAI,CAAC,EAAE;wBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;wBACtB,MAAM,KAAK,IAAI,CAAC,aAAa,EAAE,QAAQ,KAAK,IAAI,CAAC,KAAK;wBACtD,OAAO,KAAK,IAAI,CAAC,aAAa,EAAE;oBAClC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,IAAI,SAAS,aAAa,YAAY,SAAS,OAAO;gBACpD,IAAI;oBACF,yBAAyB;oBACzB,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;oBAGvC,uBAAuB;oBACvB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,QAAQ,KAAK,EACzB,MAAM;oBAET,IAAI,CAAC,cAAc;wBACjB,kBAAkB;wBAClB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,CAAC;4BACN,OAAO,QAAQ,KAAK;4BACpB,MAAM,QAAQ,IAAI,IAAI,KAAK,IAAI;4BAC/B,YAAY,QAAQ,OAAO,IAAI,KAAK,KAAK;4BACzC,gBAAgB,IAAI,OAAO,WAAW;wBACxC;wBAEF,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,wBAAwB;4BACtC,OAAO;wBACT;oBACF;oBAEA,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kBAAkB;oBAChC,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,8BAA8B;gBAC9B,IAAI;oBACF,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;oBAGvC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,QAAQ,IAAI,CAAC,KAAK,EAC9B,MAAM;oBAET,IAAI,UAAU;wBACZ,QAAQ,IAAI,CAAC,EAAE,GAAG,SAAS,EAAE;wBAC7B,QAAQ,IAAI,CAAC,IAAI,GAAG,SAAS,IAAI;wBACjC,QAAQ,IAAI,CAAC,KAAK,GAAG,SAAS,UAAU;oBAC1C;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAClC;YACF;YACA,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACzC,IAAI,MAAM;gBACR,MAAM,GAAG,GAAG,KAAK,EAAE;YACrB;YACA,OAAO;QACT;QACA,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,gBAAgB;YAChB,OAAO;QACT;QACA,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAE;YAC7B,gCAAgC;YAChC,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;iBAE7C,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YACjD,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,eAAe;IACjB;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/app/api/team/stats/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport { createClient } from '@supabase/supabase-js';\n\nconst supabase = createClient(\n  process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n);\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user) {\n      return NextResponse.json(\n        { error: 'Unauthorized', success: false },\n        { status: 401 }\n      );\n    }\n\n    // Get team members for the user's organization\n    const { data: teamMembers, error: teamError } = await supabase\n      .from('team_members')\n      .select('status, role_name, languages, specializations');\n\n    if (teamError) {\n      console.error('Database error:', teamError);\n      return NextResponse.json(\n        { error: 'Failed to fetch team stats', success: false },\n        { status: 500 }\n      );\n    }\n\n    // Get pending invitations count\n    const { count: pendingInvitations, error: invitationError } = await supabase\n      .from('team_invitations')\n      .select('*', { count: 'exact', head: true })\n      .eq('status', 'pending');\n\n    if (invitationError) {\n      console.error('Invitation count error:', invitationError);\n    }\n\n    // Calculate statistics\n    const totalMembers = teamMembers?.length || 0;\n    const activeMembers = teamMembers?.filter(m => m.status === 'active').length || 0;\n    const inactiveMembers = teamMembers?.filter(m => m.status === 'inactive').length || 0;\n\n    // Calculate by role\n    const byRole: Record<string, number> = {};\n    teamMembers?.forEach(member => {\n      byRole[member.role_name] = (byRole[member.role_name] || 0) + 1;\n    });\n\n    // Calculate by language\n    const byLanguage: Record<string, number> = {};\n    teamMembers?.forEach(member => {\n      member.languages?.forEach((language: string) => {\n        byLanguage[language] = (byLanguage[language] || 0) + 1;\n      });\n    });\n\n    // Calculate by specialization\n    const bySpecialization: Record<string, number> = {};\n    teamMembers?.forEach(member => {\n      member.specializations?.forEach((spec: string) => {\n        bySpecialization[spec] = (bySpecialization[spec] || 0) + 1;\n      });\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        totalMembers,\n        activeMembers,\n        inactiveMembers,\n        pendingInvitations: pendingInvitations || 0,\n        byRole,\n        byLanguage,\n        bySpecialization,\n      },\n    });\n  } catch (error) {\n    console.error('API error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error', success: false },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;AAGhC,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAgB,SAAS;YAAM,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,+CAA+C;QAC/C,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACnD,IAAI,CAAC,gBACL,MAAM,CAAC;QAEV,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA8B,SAAS;YAAM,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,MAAM,EAAE,OAAO,kBAAkB,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SACjE,IAAI,CAAC,oBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK,GACzC,EAAE,CAAC,UAAU;QAEhB,IAAI,iBAAiB;YACnB,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;QAEA,uBAAuB;QACvB,MAAM,eAAe,aAAa,UAAU;QAC5C,MAAM,gBAAgB,aAAa,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,UAAU;QAChF,MAAM,kBAAkB,aAAa,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,UAAU;QAEpF,oBAAoB;QACpB,MAAM,SAAiC,CAAC;QACxC,aAAa,QAAQ,CAAA;YACnB,MAAM,CAAC,OAAO,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI;QAC/D;QAEA,wBAAwB;QACxB,MAAM,aAAqC,CAAC;QAC5C,aAAa,QAAQ,CAAA;YACnB,OAAO,SAAS,EAAE,QAAQ,CAAC;gBACzB,UAAU,CAAC,SAAS,GAAG,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,IAAI;YACvD;QACF;QAEA,8BAA8B;QAC9B,MAAM,mBAA2C,CAAC;QAClD,aAAa,QAAQ,CAAA;YACnB,OAAO,eAAe,EAAE,QAAQ,CAAC;gBAC/B,gBAAgB,CAAC,KAAK,GAAG,CAAC,gBAAgB,CAAC,KAAK,IAAI,CAAC,IAAI;YAC3D;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA;gBACA;gBACA,oBAAoB,sBAAsB;gBAC1C;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAyB,SAAS;QAAM,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}