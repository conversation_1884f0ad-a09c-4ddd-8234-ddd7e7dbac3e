{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport GitHubProvider from 'next-auth/providers/github';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport { SupabaseAdapter } from '@next-auth/supabase-adapter';\nimport { createClient } from '@supabase/supabase-js';\n\nconst supabase = createClient(\n  process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n);\n\nexport const authOptions: NextAuthOptions = {\n  // Remove adapter temporarily to test without database integration\n  // adapter: SupabaseAdapter({\n  //   url: process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  //   secret: process.env.SUPABASE_SERVICE_ROLE_KEY!,\n  // }),\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_CLIENT_ID!,\n      clientSecret: process.env.GITHUB_CLIENT_SECRET!,\n    }),\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) return null;\n        \n        try {\n          const { data, error } = await supabase.auth.signInWithPassword({\n            email: credentials.email as string,\n            password: credentials.password as string,\n          });\n          \n          if (error || !data.user) return null;\n          \n          return {\n            id: data.user.id,\n            email: data.user.email!,\n            name: data.user.user_metadata?.name || data.user.email,\n            image: data.user.user_metadata?.avatar_url,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async signIn({ user, account, profile }) {\n      if (account?.provider === 'google' && profile?.email) {\n        try {\n          // Create Supabase client\n          const supabase = createClient(\n            process.env.NEXT_PUBLIC_SUPABASE_URL!,\n            process.env.SUPABASE_SERVICE_ROLE_KEY!\n          );\n\n          // Check if user exists\n          const { data: existingUser } = await supabase\n            .from('users')\n            .select('*')\n            .eq('email', profile.email)\n            .single();\n\n          if (!existingUser) {\n            // Create new user\n            const { error } = await supabase\n              .from('users')\n              .insert({\n                email: profile.email,\n                name: profile.name || user.name,\n                avatar_url: profile.picture || user.image,\n                email_verified: new Date().toISOString(),\n              });\n\n            if (error) {\n              console.error('Error creating user:', error);\n              return false;\n            }\n          }\n\n          return true;\n        } catch (error) {\n          console.error('Sign in error:', error);\n          return false;\n        }\n      }\n      return true;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        // Get user data from Supabase\n        try {\n          const supabase = createClient(\n            process.env.NEXT_PUBLIC_SUPABASE_URL!,\n            process.env.SUPABASE_SERVICE_ROLE_KEY!\n          );\n\n          const { data: userData } = await supabase\n            .from('users')\n            .select('*')\n            .eq('email', session.user.email)\n            .single();\n\n          if (userData) {\n            session.user.id = userData.id;\n            session.user.name = userData.name;\n            session.user.image = userData.avatar_url;\n          }\n        } catch (error) {\n          console.error('Session error:', error);\n        }\n      }\n      return session;\n    },\n    async jwt({ token, user, account, profile }) {\n      if (user) {\n        token.sub = user.id;\n      }\n      return token;\n    },\n    async signIn({ user, account, profile }) {\n      // Allow sign in\n      return true;\n    },\n    async redirect({ url, baseUrl }) {\n      // Allows relative callback URLs\n      if (url.startsWith('/')) return `${baseUrl}${url}`;\n      // Allows callback URLs on the same origin\n      else if (new URL(url).origin === baseUrl) return url;\n      return baseUrl;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n    error: '/auth/error',\n    verifyRequest: '/auth/verify-request',\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AAEA;;;;;AAEA,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;AAGhC,MAAM,cAA+B;IAC1C,kEAAkE;IAClE,6BAA6B;IAC7B,gDAAgD;IAChD,oDAAoD;IACpD,MAAM;IACN,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU,OAAO;gBAE1D,IAAI;oBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;wBAC7D,OAAO,YAAY,KAAK;wBACxB,UAAU,YAAY,QAAQ;oBAChC;oBAEA,IAAI,SAAS,CAAC,KAAK,IAAI,EAAE,OAAO;oBAEhC,OAAO;wBACL,IAAI,KAAK,IAAI,CAAC,EAAE;wBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;wBACtB,MAAM,KAAK,IAAI,CAAC,aAAa,EAAE,QAAQ,KAAK,IAAI,CAAC,KAAK;wBACtD,OAAO,KAAK,IAAI,CAAC,aAAa,EAAE;oBAClC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,IAAI,SAAS,aAAa,YAAY,SAAS,OAAO;gBACpD,IAAI;oBACF,yBAAyB;oBACzB,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;oBAGvC,uBAAuB;oBACvB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,QAAQ,KAAK,EACzB,MAAM;oBAET,IAAI,CAAC,cAAc;wBACjB,kBAAkB;wBAClB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,CAAC;4BACN,OAAO,QAAQ,KAAK;4BACpB,MAAM,QAAQ,IAAI,IAAI,KAAK,IAAI;4BAC/B,YAAY,QAAQ,OAAO,IAAI,KAAK,KAAK;4BACzC,gBAAgB,IAAI,OAAO,WAAW;wBACxC;wBAEF,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,wBAAwB;4BACtC,OAAO;wBACT;oBACF;oBAEA,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kBAAkB;oBAChC,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,8BAA8B;gBAC9B,IAAI;oBACF,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;oBAGvC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,QAAQ,IAAI,CAAC,KAAK,EAC9B,MAAM;oBAET,IAAI,UAAU;wBACZ,QAAQ,IAAI,CAAC,EAAE,GAAG,SAAS,EAAE;wBAC7B,QAAQ,IAAI,CAAC,IAAI,GAAG,SAAS,IAAI;wBACjC,QAAQ,IAAI,CAAC,KAAK,GAAG,SAAS,UAAU;oBAC1C;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAClC;YACF;YACA,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACzC,IAAI,MAAM;gBACR,MAAM,GAAG,GAAG,KAAK,EAAE;YACrB;YACA,OAAO;QACT;QACA,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,gBAAgB;YAChB,OAAO;QACT;QACA,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAE;YAC7B,gCAAgC;YAChC,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;iBAE7C,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YACjD,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,eAAe;IACjB;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/app/api/team/invitations/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport { createClient } from '@supabase/supabase-js';\nimport crypto from 'crypto';\n\nconst supabase = createClient(\n  process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n);\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    \n    if (!session?.user) {\n      return NextResponse.json(\n        { error: 'Unauthorized', success: false },\n        { status: 401 }\n      );\n    }\n\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '10');\n    const status = searchParams.get('status');\n\n    const offset = (page - 1) * limit;\n\n    // Build the query\n    let query = supabase\n      .from('team_invitations')\n      .select(`\n        *,\n        invited_by_user:users!team_invitations_invited_by_fkey(\n          id,\n          name,\n          email\n        )\n      `);\n\n    // Apply filters\n    if (status && status !== 'all') {\n      query = query.eq('status', status);\n    }\n\n    // Get total count\n    const { count } = await supabase\n      .from('team_invitations')\n      .select('*', { count: 'exact', head: true });\n\n    // Get paginated results\n    const { data: invitations, error } = await query\n      .range(offset, offset + limit - 1)\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Database error:', error);\n      return NextResponse.json(\n        { error: 'Failed to fetch team invitations', success: false },\n        { status: 500 }\n      );\n    }\n\n    // Get user information for invited_by users\n    const userIds = invitations?.map(inv => inv.invited_by) || [];\n    const { data: users } = await supabase.auth.admin.listUsers();\n    \n    // Create a map of user data\n    const userMap = new Map();\n    users?.users?.forEach(user => {\n      userMap.set(user.id, {\n        id: user.id,\n        name: user.user_metadata?.name || user.user_metadata?.full_name,\n        email: user.email,\n      });\n    });\n\n    // Transform the data to match the expected format\n    const transformedInvitations = invitations?.map(invitation => ({\n      id: invitation.id,\n      email: invitation.email,\n      organizationId: invitation.organization_id,\n      roleId: invitation.role_id,\n      roleName: invitation.role_name,\n      invitedBy: invitation.invited_by,\n      status: invitation.status,\n      token: invitation.token,\n      expiresAt: invitation.expires_at,\n      acceptedAt: invitation.accepted_at,\n      createdAt: invitation.created_at,\n      updatedAt: invitation.updated_at,\n      invitedByUser: userMap.get(invitation.invited_by) || {\n        id: invitation.invited_by,\n        name: 'Unknown User',\n        email: '<EMAIL>',\n      },\n    })) || [];\n\n    const totalPages = Math.ceil((count || 0) / limit);\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        items: transformedInvitations,\n        total: count || 0,\n        page,\n        limit,\n        totalPages,\n      },\n    });\n  } catch (error) {\n    console.error('API error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error', success: false },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    \n    if (!session?.user) {\n      return NextResponse.json(\n        { error: 'Unauthorized', success: false },\n        { status: 401 }\n      );\n    }\n\n    const body = await request.json();\n    const { email, roleId, message } = body;\n\n    // Validate required fields\n    if (!email || !roleId) {\n      return NextResponse.json(\n        { error: 'Missing required fields: email and roleId', success: false },\n        { status: 400 }\n      );\n    }\n\n    // Validate email format\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(email)) {\n      return NextResponse.json(\n        { error: 'Invalid email format', success: false },\n        { status: 400 }\n      );\n    }\n\n    // Check if user is already a team member\n    const { data: existingMember } = await supabase\n      .from('team_members')\n      .select('id')\n      .eq('user_id', session.user.id)\n      .eq('organization_id', session.user.organizationId || null)\n      .single();\n\n    if (existingMember) {\n      return NextResponse.json(\n        { error: 'User is already a team member', success: false },\n        { status: 400 }\n      );\n    }\n\n    // Check if there's already a pending invitation\n    const { data: existingInvitation } = await supabase\n      .from('team_invitations')\n      .select('id')\n      .eq('email', email)\n      .eq('organization_id', session.user.organizationId || null)\n      .eq('status', 'pending')\n      .single();\n\n    if (existingInvitation) {\n      return NextResponse.json(\n        { error: 'Invitation already sent to this email', success: false },\n        { status: 400 }\n      );\n    }\n\n    // Get role name\n    const { data: role, error: roleError } = await supabase\n      .from('team_roles')\n      .select('name')\n      .eq('id', roleId)\n      .single();\n\n    if (roleError || !role) {\n      return NextResponse.json(\n        { error: 'Invalid role ID', success: false },\n        { status: 400 }\n      );\n    }\n\n    // Generate invitation token\n    const token = crypto.randomBytes(32).toString('hex');\n    const expiresAt = new Date();\n    expiresAt.setDate(expiresAt.getDate() + 7); // Expires in 7 days\n\n    // Create the invitation\n    const { data: invitation, error: invitationError } = await supabase\n      .from('team_invitations')\n      .insert({\n        email,\n        organization_id: session.user.organizationId || null,\n        role_id: roleId,\n        role_name: role.name,\n        invited_by: session.user.id,\n        status: 'pending',\n        token,\n        expires_at: expiresAt.toISOString(),\n      })\n      .select()\n      .single();\n\n    if (invitationError) {\n      console.error('Invitation creation error:', invitationError);\n      return NextResponse.json(\n        { error: 'Failed to create invitation', success: false },\n        { status: 500 }\n      );\n    }\n\n    // TODO: Send invitation email\n    // This would typically integrate with an email service like SendGrid, Resend, etc.\n    console.log(`Invitation email would be sent to ${email} with token ${token}`);\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        id: invitation.id,\n        email: invitation.email,\n        organizationId: invitation.organization_id,\n        roleId: invitation.role_id,\n        roleName: invitation.role_name,\n        invitedBy: invitation.invited_by,\n        status: invitation.status,\n        token: invitation.token,\n        expiresAt: invitation.expires_at,\n        acceptedAt: invitation.accepted_at,\n        createdAt: invitation.created_at,\n        updatedAt: invitation.updated_at,\n      },\n      message: 'Team invitation sent successfully',\n    });\n  } catch (error) {\n    console.error('API error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error', success: false },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;AAGhC,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAgB,SAAS;YAAM,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,kBAAkB;QAClB,IAAI,QAAQ,SACT,IAAI,CAAC,oBACL,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC;QAEH,gBAAgB;QAChB,IAAI,UAAU,WAAW,OAAO;YAC9B,QAAQ,MAAM,EAAE,CAAC,UAAU;QAC7B;QAEA,kBAAkB;QAClB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,oBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK;QAE5C,wBAAwB;QACxB,MAAM,EAAE,MAAM,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,MACxC,KAAK,CAAC,QAAQ,SAAS,QAAQ,GAC/B,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoC,SAAS;YAAM,GAC5D;gBAAE,QAAQ;YAAI;QAElB;QAEA,4CAA4C;QAC5C,MAAM,UAAU,aAAa,IAAI,CAAA,MAAO,IAAI,UAAU,KAAK,EAAE;QAC7D,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,SAAS;QAE3D,4BAA4B;QAC5B,MAAM,UAAU,IAAI;QACpB,OAAO,OAAO,QAAQ,CAAA;YACpB,QAAQ,GAAG,CAAC,KAAK,EAAE,EAAE;gBACnB,IAAI,KAAK,EAAE;gBACX,MAAM,KAAK,aAAa,EAAE,QAAQ,KAAK,aAAa,EAAE;gBACtD,OAAO,KAAK,KAAK;YACnB;QACF;QAEA,kDAAkD;QAClD,MAAM,yBAAyB,aAAa,IAAI,CAAA,aAAc,CAAC;gBAC7D,IAAI,WAAW,EAAE;gBACjB,OAAO,WAAW,KAAK;gBACvB,gBAAgB,WAAW,eAAe;gBAC1C,QAAQ,WAAW,OAAO;gBAC1B,UAAU,WAAW,SAAS;gBAC9B,WAAW,WAAW,UAAU;gBAChC,QAAQ,WAAW,MAAM;gBACzB,OAAO,WAAW,KAAK;gBACvB,WAAW,WAAW,UAAU;gBAChC,YAAY,WAAW,WAAW;gBAClC,WAAW,WAAW,UAAU;gBAChC,WAAW,WAAW,UAAU;gBAChC,eAAe,QAAQ,GAAG,CAAC,WAAW,UAAU,KAAK;oBACnD,IAAI,WAAW,UAAU;oBACzB,MAAM;oBACN,OAAO;gBACT;YACF,CAAC,MAAM,EAAE;QAET,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP,OAAO,SAAS;gBAChB;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAyB,SAAS;QAAM,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAgB,SAAS;YAAM,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;QAEnC,2BAA2B;QAC3B,IAAI,CAAC,SAAS,CAAC,QAAQ;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA6C,SAAS;YAAM,GACrE;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAwB,SAAS;YAAM,GAChD;gBAAE,QAAQ;YAAI;QAElB;QAEA,yCAAyC;QACzC,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,gBACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QAAQ,IAAI,CAAC,EAAE,EAC7B,EAAE,CAAC,mBAAmB,QAAQ,IAAI,CAAC,cAAc,IAAI,MACrD,MAAM;QAET,IAAI,gBAAgB;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAiC,SAAS;YAAM,GACzD;gBAAE,QAAQ;YAAI;QAElB;QAEA,gDAAgD;QAChD,MAAM,EAAE,MAAM,kBAAkB,EAAE,GAAG,MAAM,SACxC,IAAI,CAAC,oBACL,MAAM,CAAC,MACP,EAAE,CAAC,SAAS,OACZ,EAAE,CAAC,mBAAmB,QAAQ,IAAI,CAAC,cAAc,IAAI,MACrD,EAAE,CAAC,UAAU,WACb,MAAM;QAET,IAAI,oBAAoB;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAyC,SAAS;YAAM,GACjE;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,cACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAmB,SAAS;YAAM,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,4BAA4B;QAC5B,MAAM,QAAQ,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC;QAC9C,MAAM,YAAY,IAAI;QACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK,IAAI,oBAAoB;QAEhE,wBAAwB;QACxB,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,oBACL,MAAM,CAAC;YACN;YACA,iBAAiB,QAAQ,IAAI,CAAC,cAAc,IAAI;YAChD,SAAS;YACT,WAAW,KAAK,IAAI;YACpB,YAAY,QAAQ,IAAI,CAAC,EAAE;YAC3B,QAAQ;YACR;YACA,YAAY,UAAU,WAAW;QACnC,GACC,MAAM,GACN,MAAM;QAET,IAAI,iBAAiB;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA+B,SAAS;YAAM,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,mFAAmF;QACnF,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,MAAM,YAAY,EAAE,OAAO;QAE5E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,IAAI,WAAW,EAAE;gBACjB,OAAO,WAAW,KAAK;gBACvB,gBAAgB,WAAW,eAAe;gBAC1C,QAAQ,WAAW,OAAO;gBAC1B,UAAU,WAAW,SAAS;gBAC9B,WAAW,WAAW,UAAU;gBAChC,QAAQ,WAAW,MAAM;gBACzB,OAAO,WAAW,KAAK;gBACvB,WAAW,WAAW,UAAU;gBAChC,YAAY,WAAW,WAAW;gBAClC,WAAW,WAAW,UAAU;gBAChC,WAAW,WAAW,UAAU;YAClC;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAyB,SAAS;QAAM,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}