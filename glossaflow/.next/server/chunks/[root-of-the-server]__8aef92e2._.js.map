{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport GitHubProvider from 'next-auth/providers/github';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport { SupabaseAdapter } from '@next-auth/supabase-adapter';\nimport { createClient } from '@supabase/supabase-js';\n\nconst supabase = createClient(\n  process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n);\n\nexport const authOptions: NextAuthOptions = {\n  // Remove adapter temporarily to test without database integration\n  // adapter: SupabaseAdapter({\n  //   url: process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  //   secret: process.env.SUPABASE_SERVICE_ROLE_KEY!,\n  // }),\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_CLIENT_ID!,\n      clientSecret: process.env.GITHUB_CLIENT_SECRET!,\n    }),\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) return null;\n        \n        try {\n          const { data, error } = await supabase.auth.signInWithPassword({\n            email: credentials.email as string,\n            password: credentials.password as string,\n          });\n          \n          if (error || !data.user) return null;\n          \n          return {\n            id: data.user.id,\n            email: data.user.email!,\n            name: data.user.user_metadata?.name || data.user.email,\n            image: data.user.user_metadata?.avatar_url,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async signIn({ user, account, profile }) {\n      if (account?.provider === 'google' && profile?.email) {\n        try {\n          // Create Supabase client\n          const supabase = createClient(\n            process.env.NEXT_PUBLIC_SUPABASE_URL!,\n            process.env.SUPABASE_SERVICE_ROLE_KEY!\n          );\n\n          // Check if user exists\n          const { data: existingUser } = await supabase\n            .from('users')\n            .select('*')\n            .eq('email', profile.email)\n            .single();\n\n          if (!existingUser) {\n            // Create new user\n            const { error } = await supabase\n              .from('users')\n              .insert({\n                email: profile.email,\n                name: profile.name || user.name,\n                avatar_url: profile.picture || user.image,\n                email_verified: new Date().toISOString(),\n              });\n\n            if (error) {\n              console.error('Error creating user:', error);\n              return false;\n            }\n          }\n\n          return true;\n        } catch (error) {\n          console.error('Sign in error:', error);\n          return false;\n        }\n      }\n      return true;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        // Get user data from Supabase\n        try {\n          const supabase = createClient(\n            process.env.NEXT_PUBLIC_SUPABASE_URL!,\n            process.env.SUPABASE_SERVICE_ROLE_KEY!\n          );\n\n          const { data: userData } = await supabase\n            .from('users')\n            .select('*')\n            .eq('email', session.user.email)\n            .single();\n\n          if (userData) {\n            session.user.id = userData.id;\n            session.user.name = userData.name;\n            session.user.image = userData.avatar_url;\n          }\n        } catch (error) {\n          console.error('Session error:', error);\n        }\n      }\n      return session;\n    },\n    async jwt({ token, user, account, profile }) {\n      if (user) {\n        token.sub = user.id;\n      }\n      return token;\n    },\n    async signIn({ user, account, profile }) {\n      // Allow sign in\n      return true;\n    },\n    async redirect({ url, baseUrl }) {\n      // Allows relative callback URLs\n      if (url.startsWith('/')) return `${baseUrl}${url}`;\n      // Allows callback URLs on the same origin\n      else if (new URL(url).origin === baseUrl) return url;\n      return baseUrl;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n    error: '/auth/error',\n    verifyRequest: '/auth/verify-request',\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AAEA;;;;;AAEA,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;AAGhC,MAAM,cAA+B;IAC1C,kEAAkE;IAClE,6BAA6B;IAC7B,gDAAgD;IAChD,oDAAoD;IACpD,MAAM;IACN,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU,OAAO;gBAE1D,IAAI;oBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;wBAC7D,OAAO,YAAY,KAAK;wBACxB,UAAU,YAAY,QAAQ;oBAChC;oBAEA,IAAI,SAAS,CAAC,KAAK,IAAI,EAAE,OAAO;oBAEhC,OAAO;wBACL,IAAI,KAAK,IAAI,CAAC,EAAE;wBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;wBACtB,MAAM,KAAK,IAAI,CAAC,aAAa,EAAE,QAAQ,KAAK,IAAI,CAAC,KAAK;wBACtD,OAAO,KAAK,IAAI,CAAC,aAAa,EAAE;oBAClC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,IAAI,SAAS,aAAa,YAAY,SAAS,OAAO;gBACpD,IAAI;oBACF,yBAAyB;oBACzB,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;oBAGvC,uBAAuB;oBACvB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,QAAQ,KAAK,EACzB,MAAM;oBAET,IAAI,CAAC,cAAc;wBACjB,kBAAkB;wBAClB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,CAAC;4BACN,OAAO,QAAQ,KAAK;4BACpB,MAAM,QAAQ,IAAI,IAAI,KAAK,IAAI;4BAC/B,YAAY,QAAQ,OAAO,IAAI,KAAK,KAAK;4BACzC,gBAAgB,IAAI,OAAO,WAAW;wBACxC;wBAEF,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,wBAAwB;4BACtC,OAAO;wBACT;oBACF;oBAEA,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kBAAkB;oBAChC,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,8BAA8B;gBAC9B,IAAI;oBACF,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;oBAGvC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,QAAQ,IAAI,CAAC,KAAK,EAC9B,MAAM;oBAET,IAAI,UAAU;wBACZ,QAAQ,IAAI,CAAC,EAAE,GAAG,SAAS,EAAE;wBAC7B,QAAQ,IAAI,CAAC,IAAI,GAAG,SAAS,IAAI;wBACjC,QAAQ,IAAI,CAAC,KAAK,GAAG,SAAS,UAAU;oBAC1C;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAClC;YACF;YACA,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACzC,IAAI,MAAM;gBACR,MAAM,GAAG,GAAG,KAAK,EAAE;YACrB;YACA,OAAO;QACT;QACA,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,gBAAgB;YAChB,OAAO;QACT;QACA,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAE;YAC7B,gCAAgC;YAChC,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;iBAE7C,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YACjD,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,eAAe;IACjB;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/app/api/team/members/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport { createClient } from '@supabase/supabase-js';\n\nconst supabase = createClient(\n  process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n);\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n\n    if (!session?.user) {\n      return NextResponse.json(\n        { error: 'Unauthorized', success: false },\n        { status: 401 }\n      );\n    }\n\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '10');\n    const search = searchParams.get('search');\n    const role = searchParams.get('role');\n    const status = searchParams.get('status');\n    const language = searchParams.get('language');\n    const specialization = searchParams.get('specialization');\n\n    const offset = (page - 1) * limit;\n\n    // Build the query with joins to get user info and stats\n    let query = supabase\n      .from('team_members')\n      .select(`\n        *,\n        stats:team_member_stats(\n          projects_completed,\n          words_translated,\n          segments_translated,\n          segments_reviewed,\n          average_rating,\n          current_projects,\n          total_hours_worked\n        )\n      `);\n\n    // Apply filters\n    if (search) {\n      // Search in user name and email through the joined table\n      query = query.or(`users.name.ilike.%${search}%,users.email.ilike.%${search}%`);\n    }\n    if (role && role !== 'all') {\n      query = query.eq('role_name', role);\n    }\n    if (status && status !== 'all') {\n      query = query.eq('status', status);\n    }\n    if (language && language !== 'all') {\n      query = query.contains('languages', [language]);\n    }\n    if (specialization && specialization !== 'all') {\n      query = query.contains('specializations', [specialization]);\n    }\n\n    // Get total count\n    const { count } = await supabase\n      .from('team_members')\n      .select('*', { count: 'exact', head: true });\n\n    // Get paginated results\n    const { data: teamMembers, error } = await query\n      .range(offset, offset + limit - 1)\n      .order('joined_at', { ascending: false });\n\n    if (error) {\n      console.error('Database error:', error);\n      return NextResponse.json(\n        { error: 'Failed to fetch team members', success: false },\n        { status: 500 }\n      );\n    }\n\n    // Get user information for each team member\n    const userIds = teamMembers?.map(member => member.user_id) || [];\n    const { data: users } = await supabase.auth.admin.listUsers();\n\n    // Create a map of user data\n    const userMap = new Map();\n    users?.users?.forEach(user => {\n      userMap.set(user.id, {\n        id: user.id,\n        name: user.user_metadata?.name || user.user_metadata?.full_name,\n        email: user.email,\n        avatar: user.user_metadata?.avatar_url || user.user_metadata?.picture,\n      });\n    });\n\n    // Transform the data to match the expected format\n    const transformedTeamMembers = teamMembers?.map(member => ({\n      id: member.id,\n      userId: member.user_id,\n      organizationId: member.organization_id,\n      roleId: member.role_id,\n      roleName: member.role_name,\n      status: member.status,\n      languages: member.languages || [],\n      specializations: member.specializations || [],\n      bio: member.bio,\n      hourlyRate: member.hourly_rate,\n      timezone: member.timezone,\n      joinedAt: member.joined_at,\n      lastActiveAt: member.last_active_at,\n      createdAt: member.created_at,\n      updatedAt: member.updated_at,\n      createdBy: member.created_by,\n      user: userMap.get(member.user_id) || {\n        id: member.user_id,\n        name: 'Unknown User',\n        email: '<EMAIL>',\n        avatar: null,\n      },\n      stats: member.stats?.[0] ? {\n        projectsCompleted: member.stats[0].projects_completed || 0,\n        wordsTranslated: member.stats[0].words_translated || 0,\n        segmentsTranslated: member.stats[0].segments_translated || 0,\n        segmentsReviewed: member.stats[0].segments_reviewed || 0,\n        averageRating: member.stats[0].average_rating || 0,\n        currentProjects: member.stats[0].current_projects || 0,\n        totalHoursWorked: member.stats[0].total_hours_worked || 0,\n      } : {\n        projectsCompleted: 0,\n        wordsTranslated: 0,\n        segmentsTranslated: 0,\n        segmentsReviewed: 0,\n        averageRating: 0,\n        currentProjects: 0,\n        totalHoursWorked: 0,\n      },\n    })) || [];\n\n    const totalPages = Math.ceil((count || 0) / limit);\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        items: transformedTeamMembers,\n        total: count || 0,\n        page,\n        limit,\n        totalPages,\n      },\n    });\n  } catch (error) {\n    console.error('API error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error', success: false },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    \n    if (!session?.user?.email) {\n      return NextResponse.json(\n        { error: 'Unauthorized', success: false },\n        { status: 401 }\n      );\n    }\n\n    const body = await request.json();\n    const {\n      userId,\n      roleId,\n      languages,\n      specializations,\n      bio,\n      hourlyRate,\n      timezone,\n    } = body;\n\n    // Validate required fields\n    if (!userId || !roleId) {\n      return NextResponse.json(\n        { error: 'Missing required fields: userId and roleId', success: false },\n        { status: 400 }\n      );\n    }\n\n    // Get role name\n    const { data: role, error: roleError } = await supabase\n      .from('team_roles')\n      .select('name')\n      .eq('id', roleId)\n      .single();\n\n    if (roleError || !role) {\n      return NextResponse.json(\n        { error: 'Invalid role ID', success: false },\n        { status: 400 }\n      );\n    }\n\n    // Create the team member\n    const { data: member, error: memberError } = await supabase\n      .from('team_members')\n      .insert({\n        user_id: userId,\n        organization_id: session.user.organizationId || null,\n        role_id: roleId,\n        role_name: role.name,\n        status: 'active',\n        languages: languages || [],\n        specializations: specializations || [],\n        bio,\n        hourly_rate: hourlyRate,\n        timezone: timezone || 'UTC',\n        created_by: session.user.id,\n      })\n      .select(`\n        *,\n        user:users!team_members_user_id_fkey(\n          id,\n          name,\n          email,\n          avatar_url\n        )\n      `)\n      .single();\n\n    if (memberError) {\n      console.error('Team member creation error:', memberError);\n      return NextResponse.json(\n        { error: 'Failed to create team member', success: false },\n        { status: 500 }\n      );\n    }\n\n    // Create initial stats record\n    await supabase\n      .from('team_member_stats')\n      .insert({\n        team_member_id: member.id,\n      });\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        id: member.id,\n        userId: member.user_id,\n        organizationId: member.organization_id,\n        roleId: member.role_id,\n        roleName: member.role_name,\n        status: member.status,\n        languages: member.languages || [],\n        specializations: member.specializations || [],\n        bio: member.bio,\n        hourlyRate: member.hourly_rate,\n        timezone: member.timezone,\n        joinedAt: member.joined_at,\n        lastActiveAt: member.last_active_at,\n        createdAt: member.created_at,\n        updatedAt: member.updated_at,\n        createdBy: member.created_by,\n        user: member.user ? {\n          id: member.user.id,\n          name: member.user.name,\n          email: member.user.email,\n          avatar: member.user.avatar_url,\n        } : undefined,\n      },\n      message: 'Team member created successfully',\n    });\n  } catch (error) {\n    console.error('API error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error', success: false },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;AAGhC,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAgB,SAAS;YAAM,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,iBAAiB,aAAa,GAAG,CAAC;QAExC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,wDAAwD;QACxD,IAAI,QAAQ,SACT,IAAI,CAAC,gBACL,MAAM,CAAC,CAAC;;;;;;;;;;;MAWT,CAAC;QAEH,gBAAgB;QAChB,IAAI,QAAQ;YACV,yDAAyD;YACzD,QAAQ,MAAM,EAAE,CAAC,CAAC,kBAAkB,EAAE,OAAO,qBAAqB,EAAE,OAAO,CAAC,CAAC;QAC/E;QACA,IAAI,QAAQ,SAAS,OAAO;YAC1B,QAAQ,MAAM,EAAE,CAAC,aAAa;QAChC;QACA,IAAI,UAAU,WAAW,OAAO;YAC9B,QAAQ,MAAM,EAAE,CAAC,UAAU;QAC7B;QACA,IAAI,YAAY,aAAa,OAAO;YAClC,QAAQ,MAAM,QAAQ,CAAC,aAAa;gBAAC;aAAS;QAChD;QACA,IAAI,kBAAkB,mBAAmB,OAAO;YAC9C,QAAQ,MAAM,QAAQ,CAAC,mBAAmB;gBAAC;aAAe;QAC5D;QAEA,kBAAkB;QAClB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,gBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK;QAE5C,wBAAwB;QACxB,MAAM,EAAE,MAAM,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,MACxC,KAAK,CAAC,QAAQ,SAAS,QAAQ,GAC/B,KAAK,CAAC,aAAa;YAAE,WAAW;QAAM;QAEzC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAgC,SAAS;YAAM,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,4CAA4C;QAC5C,MAAM,UAAU,aAAa,IAAI,CAAA,SAAU,OAAO,OAAO,KAAK,EAAE;QAChE,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,SAAS;QAE3D,4BAA4B;QAC5B,MAAM,UAAU,IAAI;QACpB,OAAO,OAAO,QAAQ,CAAA;YACpB,QAAQ,GAAG,CAAC,KAAK,EAAE,EAAE;gBACnB,IAAI,KAAK,EAAE;gBACX,MAAM,KAAK,aAAa,EAAE,QAAQ,KAAK,aAAa,EAAE;gBACtD,OAAO,KAAK,KAAK;gBACjB,QAAQ,KAAK,aAAa,EAAE,cAAc,KAAK,aAAa,EAAE;YAChE;QACF;QAEA,kDAAkD;QAClD,MAAM,yBAAyB,aAAa,IAAI,CAAA,SAAU,CAAC;gBACzD,IAAI,OAAO,EAAE;gBACb,QAAQ,OAAO,OAAO;gBACtB,gBAAgB,OAAO,eAAe;gBACtC,QAAQ,OAAO,OAAO;gBACtB,UAAU,OAAO,SAAS;gBAC1B,QAAQ,OAAO,MAAM;gBACrB,WAAW,OAAO,SAAS,IAAI,EAAE;gBACjC,iBAAiB,OAAO,eAAe,IAAI,EAAE;gBAC7C,KAAK,OAAO,GAAG;gBACf,YAAY,OAAO,WAAW;gBAC9B,UAAU,OAAO,QAAQ;gBACzB,UAAU,OAAO,SAAS;gBAC1B,cAAc,OAAO,cAAc;gBACnC,WAAW,OAAO,UAAU;gBAC5B,WAAW,OAAO,UAAU;gBAC5B,WAAW,OAAO,UAAU;gBAC5B,MAAM,QAAQ,GAAG,CAAC,OAAO,OAAO,KAAK;oBACnC,IAAI,OAAO,OAAO;oBAClB,MAAM;oBACN,OAAO;oBACP,QAAQ;gBACV;gBACA,OAAO,OAAO,KAAK,EAAE,CAAC,EAAE,GAAG;oBACzB,mBAAmB,OAAO,KAAK,CAAC,EAAE,CAAC,kBAAkB,IAAI;oBACzD,iBAAiB,OAAO,KAAK,CAAC,EAAE,CAAC,gBAAgB,IAAI;oBACrD,oBAAoB,OAAO,KAAK,CAAC,EAAE,CAAC,mBAAmB,IAAI;oBAC3D,kBAAkB,OAAO,KAAK,CAAC,EAAE,CAAC,iBAAiB,IAAI;oBACvD,eAAe,OAAO,KAAK,CAAC,EAAE,CAAC,cAAc,IAAI;oBACjD,iBAAiB,OAAO,KAAK,CAAC,EAAE,CAAC,gBAAgB,IAAI;oBACrD,kBAAkB,OAAO,KAAK,CAAC,EAAE,CAAC,kBAAkB,IAAI;gBAC1D,IAAI;oBACF,mBAAmB;oBACnB,iBAAiB;oBACjB,oBAAoB;oBACpB,kBAAkB;oBAClB,eAAe;oBACf,iBAAiB;oBACjB,kBAAkB;gBACpB;YACF,CAAC,MAAM,EAAE;QAET,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP,OAAO,SAAS;gBAChB;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAyB,SAAS;QAAM,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAgB,SAAS;YAAM,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,MAAM,EACN,MAAM,EACN,SAAS,EACT,eAAe,EACf,GAAG,EACH,UAAU,EACV,QAAQ,EACT,GAAG;QAEJ,2BAA2B;QAC3B,IAAI,CAAC,UAAU,CAAC,QAAQ;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA8C,SAAS;YAAM,GACtE;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,cACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAmB,SAAS;YAAM,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,gBACL,MAAM,CAAC;YACN,SAAS;YACT,iBAAiB,QAAQ,IAAI,CAAC,cAAc,IAAI;YAChD,SAAS;YACT,WAAW,KAAK,IAAI;YACpB,QAAQ;YACR,WAAW,aAAa,EAAE;YAC1B,iBAAiB,mBAAmB,EAAE;YACtC;YACA,aAAa;YACb,UAAU,YAAY;YACtB,YAAY,QAAQ,IAAI,CAAC,EAAE;QAC7B,GACC,MAAM,CAAC,CAAC;;;;;;;;MAQT,CAAC,EACA,MAAM;QAET,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAgC,SAAS;YAAM,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,SACH,IAAI,CAAC,qBACL,MAAM,CAAC;YACN,gBAAgB,OAAO,EAAE;QAC3B;QAEF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,IAAI,OAAO,EAAE;gBACb,QAAQ,OAAO,OAAO;gBACtB,gBAAgB,OAAO,eAAe;gBACtC,QAAQ,OAAO,OAAO;gBACtB,UAAU,OAAO,SAAS;gBAC1B,QAAQ,OAAO,MAAM;gBACrB,WAAW,OAAO,SAAS,IAAI,EAAE;gBACjC,iBAAiB,OAAO,eAAe,IAAI,EAAE;gBAC7C,KAAK,OAAO,GAAG;gBACf,YAAY,OAAO,WAAW;gBAC9B,UAAU,OAAO,QAAQ;gBACzB,UAAU,OAAO,SAAS;gBAC1B,cAAc,OAAO,cAAc;gBACnC,WAAW,OAAO,UAAU;gBAC5B,WAAW,OAAO,UAAU;gBAC5B,WAAW,OAAO,UAAU;gBAC5B,MAAM,OAAO,IAAI,GAAG;oBAClB,IAAI,OAAO,IAAI,CAAC,EAAE;oBAClB,MAAM,OAAO,IAAI,CAAC,IAAI;oBACtB,OAAO,OAAO,IAAI,CAAC,KAAK;oBACxB,QAAQ,OAAO,IAAI,CAAC,UAAU;gBAChC,IAAI;YACN;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAyB,SAAS;QAAM,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}