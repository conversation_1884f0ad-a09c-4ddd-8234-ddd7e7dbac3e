module.exports = {

"[project]/.next-internal/server/app/api/team/members/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/google.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$github$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/github.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
;
;
;
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://awhtjodmtstnsobmagfc.supabase.co"), process.env.SUPABASE_SERVICE_ROLE_KEY);
const authOptions = {
    // Remove adapter temporarily to test without database integration
    // adapter: SupabaseAdapter({
    //   url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    //   secret: process.env.SUPABASE_SERVICE_ROLE_KEY!,
    // }),
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$github$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.GITHUB_CLIENT_ID,
            clientSecret: process.env.GITHUB_CLIENT_SECRET
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                password: {
                    label: 'Password',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.password) return null;
                try {
                    const { data, error } = await supabase.auth.signInWithPassword({
                        email: credentials.email,
                        password: credentials.password
                    });
                    if (error || !data.user) return null;
                    return {
                        id: data.user.id,
                        email: data.user.email,
                        name: data.user.user_metadata?.name || data.user.email,
                        image: data.user.user_metadata?.avatar_url
                    };
                } catch (error) {
                    console.error('Auth error:', error);
                    return null;
                }
            }
        })
    ],
    callbacks: {
        async signIn ({ user, account, profile }) {
            if (account?.provider === 'google' && profile?.email) {
                try {
                    // Create Supabase client
                    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://awhtjodmtstnsobmagfc.supabase.co"), process.env.SUPABASE_SERVICE_ROLE_KEY);
                    // Check if user exists
                    const { data: existingUser } = await supabase.from('users').select('*').eq('email', profile.email).single();
                    if (!existingUser) {
                        // Create new user
                        const { error } = await supabase.from('users').insert({
                            email: profile.email,
                            name: profile.name || user.name,
                            avatar_url: profile.picture || user.image,
                            email_verified: new Date().toISOString()
                        });
                        if (error) {
                            console.error('Error creating user:', error);
                            return false;
                        }
                    }
                    return true;
                } catch (error) {
                    console.error('Sign in error:', error);
                    return false;
                }
            }
            return true;
        },
        async session ({ session, token }) {
            if (token && session.user) {
                // Get user data from Supabase
                try {
                    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://awhtjodmtstnsobmagfc.supabase.co"), process.env.SUPABASE_SERVICE_ROLE_KEY);
                    const { data: userData } = await supabase.from('users').select('*').eq('email', session.user.email).single();
                    if (userData) {
                        session.user.id = userData.id;
                        session.user.name = userData.name;
                        session.user.image = userData.avatar_url;
                    }
                } catch (error) {
                    console.error('Session error:', error);
                }
            }
            return session;
        },
        async jwt ({ token, user, account, profile }) {
            if (user) {
                token.sub = user.id;
            }
            return token;
        },
        async signIn ({ user, account, profile }) {
            // Allow sign in
            return true;
        },
        async redirect ({ url, baseUrl }) {
            // Allows relative callback URLs
            if (url.startsWith('/')) return `${baseUrl}${url}`;
            else if (new URL(url).origin === baseUrl) return url;
            return baseUrl;
        }
    },
    pages: {
        signIn: '/auth/signin',
        signUp: '/auth/signup',
        error: '/auth/error',
        verifyRequest: '/auth/verify-request'
    },
    session: {
        strategy: 'jwt'
    },
    secret: process.env.NEXTAUTH_SECRET
};
}}),
"[project]/src/app/api/team/members/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/next/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
;
;
;
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://awhtjodmtstnsobmagfc.supabase.co"), process.env.SUPABASE_SERVICE_ROLE_KEY);
async function GET(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session?.user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized',
                success: false
            }, {
                status: 401
            });
        }
        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const search = searchParams.get('search');
        const role = searchParams.get('role');
        const status = searchParams.get('status');
        const language = searchParams.get('language');
        const specialization = searchParams.get('specialization');
        const offset = (page - 1) * limit;
        // Build the query with joins to get user info and stats
        let query = supabase.from('team_members').select(`
        *,
        stats:team_member_stats(
          projects_completed,
          words_translated,
          segments_translated,
          segments_reviewed,
          average_rating,
          current_projects,
          total_hours_worked
        )
      `);
        // Apply filters
        if (search) {
            // Search in user name and email through the joined table
            query = query.or(`users.name.ilike.%${search}%,users.email.ilike.%${search}%`);
        }
        if (role && role !== 'all') {
            query = query.eq('role_name', role);
        }
        if (status && status !== 'all') {
            query = query.eq('status', status);
        }
        if (language && language !== 'all') {
            query = query.contains('languages', [
                language
            ]);
        }
        if (specialization && specialization !== 'all') {
            query = query.contains('specializations', [
                specialization
            ]);
        }
        // Get total count
        const { count } = await supabase.from('team_members').select('*', {
            count: 'exact',
            head: true
        });
        // Get paginated results
        const { data: teamMembers, error } = await query.range(offset, offset + limit - 1).order('joined_at', {
            ascending: false
        });
        if (error) {
            console.error('Database error:', error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to fetch team members',
                success: false
            }, {
                status: 500
            });
        }
        // Get user information for each team member
        const userIds = teamMembers?.map((member)=>member.user_id) || [];
        const { data: users } = await supabase.auth.admin.listUsers();
        // Create a map of user data
        const userMap = new Map();
        users?.users?.forEach((user)=>{
            userMap.set(user.id, {
                id: user.id,
                name: user.user_metadata?.name || user.user_metadata?.full_name,
                email: user.email,
                avatar: user.user_metadata?.avatar_url || user.user_metadata?.picture
            });
        });
        // Transform the data to match the expected format
        const transformedTeamMembers = teamMembers?.map((member)=>({
                id: member.id,
                userId: member.user_id,
                organizationId: member.organization_id,
                roleId: member.role_id,
                roleName: member.role_name,
                status: member.status,
                languages: member.languages || [],
                specializations: member.specializations || [],
                bio: member.bio,
                hourlyRate: member.hourly_rate,
                timezone: member.timezone,
                joinedAt: member.joined_at,
                lastActiveAt: member.last_active_at,
                createdAt: member.created_at,
                updatedAt: member.updated_at,
                createdBy: member.created_by,
                user: userMap.get(member.user_id) || {
                    id: member.user_id,
                    name: 'Unknown User',
                    email: '<EMAIL>',
                    avatar: null
                },
                stats: member.stats?.[0] ? {
                    projectsCompleted: member.stats[0].projects_completed || 0,
                    wordsTranslated: member.stats[0].words_translated || 0,
                    segmentsTranslated: member.stats[0].segments_translated || 0,
                    segmentsReviewed: member.stats[0].segments_reviewed || 0,
                    averageRating: member.stats[0].average_rating || 0,
                    currentProjects: member.stats[0].current_projects || 0,
                    totalHoursWorked: member.stats[0].total_hours_worked || 0
                } : {
                    projectsCompleted: 0,
                    wordsTranslated: 0,
                    segmentsTranslated: 0,
                    segmentsReviewed: 0,
                    averageRating: 0,
                    currentProjects: 0,
                    totalHoursWorked: 0
                }
            })) || [];
        const totalPages = Math.ceil((count || 0) / limit);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                items: transformedTeamMembers,
                total: count || 0,
                page,
                limit,
                totalPages
            }
        });
    } catch (error) {
        console.error('API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error',
            success: false
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session?.user?.email) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized',
                success: false
            }, {
                status: 401
            });
        }
        const body = await request.json();
        const { userId, roleId, languages, specializations, bio, hourlyRate, timezone } = body;
        // Validate required fields
        if (!userId || !roleId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Missing required fields: userId and roleId',
                success: false
            }, {
                status: 400
            });
        }
        // Get role name
        const { data: role, error: roleError } = await supabase.from('team_roles').select('name').eq('id', roleId).single();
        if (roleError || !role) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid role ID',
                success: false
            }, {
                status: 400
            });
        }
        // Create the team member
        const { data: member, error: memberError } = await supabase.from('team_members').insert({
            user_id: userId,
            organization_id: session.user.organizationId || null,
            role_id: roleId,
            role_name: role.name,
            status: 'active',
            languages: languages || [],
            specializations: specializations || [],
            bio,
            hourly_rate: hourlyRate,
            timezone: timezone || 'UTC',
            created_by: session.user.id
        }).select(`
        *,
        user:users!team_members_user_id_fkey(
          id,
          name,
          email,
          avatar_url
        )
      `).single();
        if (memberError) {
            console.error('Team member creation error:', memberError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to create team member',
                success: false
            }, {
                status: 500
            });
        }
        // Create initial stats record
        await supabase.from('team_member_stats').insert({
            team_member_id: member.id
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                id: member.id,
                userId: member.user_id,
                organizationId: member.organization_id,
                roleId: member.role_id,
                roleName: member.role_name,
                status: member.status,
                languages: member.languages || [],
                specializations: member.specializations || [],
                bio: member.bio,
                hourlyRate: member.hourly_rate,
                timezone: member.timezone,
                joinedAt: member.joined_at,
                lastActiveAt: member.last_active_at,
                createdAt: member.created_at,
                updatedAt: member.updated_at,
                createdBy: member.created_by,
                user: member.user ? {
                    id: member.user.id,
                    name: member.user.name,
                    email: member.user.email,
                    avatar: member.user.avatar_url
                } : undefined
            },
            message: 'Team member created successfully'
        });
    } catch (error) {
        console.error('API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error',
            success: false
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__8aef92e2._.js.map