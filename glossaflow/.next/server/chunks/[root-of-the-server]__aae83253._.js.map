{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport GitHubProvider from 'next-auth/providers/github';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport { SupabaseAdapter } from '@next-auth/supabase-adapter';\nimport { createClient } from '@supabase/supabase-js';\n\nconst supabase = createClient(\n  process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n);\n\nexport const authOptions: NextAuthOptions = {\n  // Remove adapter temporarily to test without database integration\n  // adapter: SupabaseAdapter({\n  //   url: process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  //   secret: process.env.SUPABASE_SERVICE_ROLE_KEY!,\n  // }),\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_CLIENT_ID!,\n      clientSecret: process.env.GITHUB_CLIENT_SECRET!,\n    }),\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) return null;\n        \n        try {\n          const { data, error } = await supabase.auth.signInWithPassword({\n            email: credentials.email as string,\n            password: credentials.password as string,\n          });\n          \n          if (error || !data.user) return null;\n          \n          return {\n            id: data.user.id,\n            email: data.user.email!,\n            name: data.user.user_metadata?.name || data.user.email,\n            image: data.user.user_metadata?.avatar_url,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async signIn({ user, account, profile }) {\n      if (account?.provider === 'google' && profile?.email) {\n        try {\n          // Create Supabase client\n          const supabase = createClient(\n            process.env.NEXT_PUBLIC_SUPABASE_URL!,\n            process.env.SUPABASE_SERVICE_ROLE_KEY!\n          );\n\n          // Check if user exists\n          const { data: existingUser } = await supabase\n            .from('users')\n            .select('*')\n            .eq('email', profile.email)\n            .single();\n\n          if (!existingUser) {\n            // Create new user\n            const { error } = await supabase\n              .from('users')\n              .insert({\n                email: profile.email,\n                name: profile.name || user.name,\n                avatar_url: profile.picture || user.image,\n                email_verified: new Date().toISOString(),\n              });\n\n            if (error) {\n              console.error('Error creating user:', error);\n              return false;\n            }\n          }\n\n          return true;\n        } catch (error) {\n          console.error('Sign in error:', error);\n          return false;\n        }\n      }\n      return true;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        // Get user data from Supabase\n        try {\n          const supabase = createClient(\n            process.env.NEXT_PUBLIC_SUPABASE_URL!,\n            process.env.SUPABASE_SERVICE_ROLE_KEY!\n          );\n\n          const { data: userData } = await supabase\n            .from('users')\n            .select('*')\n            .eq('email', session.user.email)\n            .single();\n\n          if (userData) {\n            session.user.id = userData.id;\n            session.user.name = userData.name;\n            session.user.image = userData.avatar_url;\n          }\n        } catch (error) {\n          console.error('Session error:', error);\n        }\n      }\n      return session;\n    },\n    async jwt({ token, user, account, profile }) {\n      if (user) {\n        token.sub = user.id;\n      }\n      return token;\n    },\n    async signIn({ user, account, profile }) {\n      // Allow sign in\n      return true;\n    },\n    async redirect({ url, baseUrl }) {\n      // Allows relative callback URLs\n      if (url.startsWith('/')) return `${baseUrl}${url}`;\n      // Allows callback URLs on the same origin\n      else if (new URL(url).origin === baseUrl) return url;\n      return baseUrl;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n    error: '/auth/error',\n    verifyRequest: '/auth/verify-request',\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AAEA;;;;;AAEA,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;AAGhC,MAAM,cAA+B;IAC1C,kEAAkE;IAClE,6BAA6B;IAC7B,gDAAgD;IAChD,oDAAoD;IACpD,MAAM;IACN,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU,OAAO;gBAE1D,IAAI;oBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;wBAC7D,OAAO,YAAY,KAAK;wBACxB,UAAU,YAAY,QAAQ;oBAChC;oBAEA,IAAI,SAAS,CAAC,KAAK,IAAI,EAAE,OAAO;oBAEhC,OAAO;wBACL,IAAI,KAAK,IAAI,CAAC,EAAE;wBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;wBACtB,MAAM,KAAK,IAAI,CAAC,aAAa,EAAE,QAAQ,KAAK,IAAI,CAAC,KAAK;wBACtD,OAAO,KAAK,IAAI,CAAC,aAAa,EAAE;oBAClC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,IAAI,SAAS,aAAa,YAAY,SAAS,OAAO;gBACpD,IAAI;oBACF,yBAAyB;oBACzB,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;oBAGvC,uBAAuB;oBACvB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,QAAQ,KAAK,EACzB,MAAM;oBAET,IAAI,CAAC,cAAc;wBACjB,kBAAkB;wBAClB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,CAAC;4BACN,OAAO,QAAQ,KAAK;4BACpB,MAAM,QAAQ,IAAI,IAAI,KAAK,IAAI;4BAC/B,YAAY,QAAQ,OAAO,IAAI,KAAK,KAAK;4BACzC,gBAAgB,IAAI,OAAO,WAAW;wBACxC;wBAEF,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,wBAAwB;4BACtC,OAAO;wBACT;oBACF;oBAEA,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kBAAkB;oBAChC,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,8BAA8B;gBAC9B,IAAI;oBACF,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;oBAGvC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,QAAQ,IAAI,CAAC,KAAK,EAC9B,MAAM;oBAET,IAAI,UAAU;wBACZ,QAAQ,IAAI,CAAC,EAAE,GAAG,SAAS,EAAE;wBAC7B,QAAQ,IAAI,CAAC,IAAI,GAAG,SAAS,IAAI;wBACjC,QAAQ,IAAI,CAAC,KAAK,GAAG,SAAS,UAAU;oBAC1C;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAClC;YACF;YACA,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACzC,IAAI,MAAM;gBACR,MAAM,GAAG,GAAG,KAAK,EAAE;YACrB;YACA,OAAO;QACT;QACA,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,gBAAgB;YAChB,OAAO;QACT;QACA,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAE;YAC7B,gCAAgC;YAChC,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;iBAE7C,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YACjD,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,eAAe;IACjB;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/app/api/terminology/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport { createClient } from '@supabase/supabase-js';\n\nconst supabase = createClient(\n  process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n);\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    \n    if (!session?.user?.email) {\n      return NextResponse.json(\n        { error: 'Unauthorized', success: false },\n        { status: 401 }\n      );\n    }\n\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '10');\n    const search = searchParams.get('search');\n    const category = searchParams.get('category');\n    const targetLanguage = searchParams.get('targetLanguage');\n    const approvalStatus = searchParams.get('approvalStatus');\n    const projectId = searchParams.get('projectId');\n\n    const offset = (page - 1) * limit;\n\n    // Build the query\n    let query = supabase\n      .from('terminology_entries')\n      .select(`\n        *,\n        created_by_user:users!terminology_entries_created_by_fkey(\n          id,\n          name,\n          email\n        ),\n        reviewed_by_user:users!terminology_entries_reviewed_by_fkey(\n          id,\n          name,\n          email\n        )\n      `);\n\n    // Apply filters\n    if (search) {\n      query = query.or(`source_term.ilike.%${search}%,target_term.ilike.%${search}%`);\n    }\n    if (category && category !== 'all') {\n      query = query.eq('category', category);\n    }\n    if (targetLanguage && targetLanguage !== 'all') {\n      query = query.eq('target_language', targetLanguage);\n    }\n    if (approvalStatus && approvalStatus !== 'all') {\n      query = query.eq('approval_status', approvalStatus);\n    }\n    if (projectId) {\n      query = query.eq('project_id', projectId);\n    }\n\n    // Get total count\n    const { count } = await supabase\n      .from('terminology_entries')\n      .select('*', { count: 'exact', head: true });\n\n    // Get paginated results\n    const { data: terminology, error } = await query\n      .range(offset, offset + limit - 1)\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Database error:', error);\n      return NextResponse.json(\n        { error: 'Failed to fetch terminology', success: false },\n        { status: 500 }\n      );\n    }\n\n    // Transform the data to match the expected format\n    const transformedTerminology = terminology?.map(entry => ({\n      id: entry.id,\n      sourceTerm: entry.source_term,\n      targetTerm: entry.target_term,\n      targetLanguage: entry.target_language,\n      category: entry.category,\n      context: entry.context,\n      usageNotes: entry.usage_notes,\n      approvalStatus: entry.approval_status,\n      frequency: entry.frequency || 0,\n      createdBy: entry.created_by,\n      reviewedBy: entry.reviewed_by,\n      lastUsed: entry.last_used,\n      createdAt: entry.created_at,\n      updatedAt: entry.updated_at,\n      organizationId: entry.organization_id,\n      projectId: entry.project_id,\n    })) || [];\n\n    const totalPages = Math.ceil((count || 0) / limit);\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        items: transformedTerminology,\n        total: count || 0,\n        page,\n        limit,\n        totalPages,\n      },\n    });\n  } catch (error) {\n    console.error('API error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error', success: false },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    \n    if (!session?.user?.email) {\n      return NextResponse.json(\n        { error: 'Unauthorized', success: false },\n        { status: 401 }\n      );\n    }\n\n    const body = await request.json();\n    const {\n      sourceTerm,\n      targetTerm,\n      targetLanguage,\n      category,\n      context,\n      usageNotes,\n      projectId,\n    } = body;\n\n    // Validate required fields\n    if (!sourceTerm || !targetTerm || !targetLanguage || !category) {\n      return NextResponse.json(\n        { error: 'Missing required fields', success: false },\n        { status: 400 }\n      );\n    }\n\n    // Create the terminology entry\n    const { data: entry, error: entryError } = await supabase\n      .from('terminology_entries')\n      .insert({\n        source_term: sourceTerm,\n        target_term: targetTerm,\n        target_language: targetLanguage,\n        category,\n        context,\n        usage_notes: usageNotes,\n        project_id: projectId,\n        created_by: session.user.id,\n        organization_id: session.user.organizationId || null,\n        approval_status: 'pending',\n        frequency: 0,\n      })\n      .select()\n      .single();\n\n    if (entryError) {\n      console.error('Terminology creation error:', entryError);\n      return NextResponse.json(\n        { error: 'Failed to create terminology entry', success: false },\n        { status: 500 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        id: entry.id,\n        sourceTerm: entry.source_term,\n        targetTerm: entry.target_term,\n        targetLanguage: entry.target_language,\n        category: entry.category,\n        context: entry.context,\n        usageNotes: entry.usage_notes,\n        approvalStatus: entry.approval_status,\n        frequency: entry.frequency || 0,\n        createdBy: entry.created_by,\n        reviewedBy: entry.reviewed_by,\n        lastUsed: entry.last_used,\n        createdAt: entry.created_at,\n        updatedAt: entry.updated_at,\n        organizationId: entry.organization_id,\n        projectId: entry.project_id,\n      },\n      message: 'Terminology entry created successfully',\n    });\n  } catch (error) {\n    console.error('API error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error', success: false },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB;AAGhC,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAgB,SAAS;YAAM,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,iBAAiB,aAAa,GAAG,CAAC;QACxC,MAAM,iBAAiB,aAAa,GAAG,CAAC;QACxC,MAAM,YAAY,aAAa,GAAG,CAAC;QAEnC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,kBAAkB;QAClB,IAAI,QAAQ,SACT,IAAI,CAAC,uBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;MAYT,CAAC;QAEH,gBAAgB;QAChB,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,CAAC,mBAAmB,EAAE,OAAO,qBAAqB,EAAE,OAAO,CAAC,CAAC;QAChF;QACA,IAAI,YAAY,aAAa,OAAO;YAClC,QAAQ,MAAM,EAAE,CAAC,YAAY;QAC/B;QACA,IAAI,kBAAkB,mBAAmB,OAAO;YAC9C,QAAQ,MAAM,EAAE,CAAC,mBAAmB;QACtC;QACA,IAAI,kBAAkB,mBAAmB,OAAO;YAC9C,QAAQ,MAAM,EAAE,CAAC,mBAAmB;QACtC;QACA,IAAI,WAAW;YACb,QAAQ,MAAM,EAAE,CAAC,cAAc;QACjC;QAEA,kBAAkB;QAClB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,uBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK;QAE5C,wBAAwB;QACxB,MAAM,EAAE,MAAM,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,MACxC,KAAK,CAAC,QAAQ,SAAS,QAAQ,GAC/B,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA+B,SAAS;YAAM,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,kDAAkD;QAClD,MAAM,yBAAyB,aAAa,IAAI,CAAA,QAAS,CAAC;gBACxD,IAAI,MAAM,EAAE;gBACZ,YAAY,MAAM,WAAW;gBAC7B,YAAY,MAAM,WAAW;gBAC7B,gBAAgB,MAAM,eAAe;gBACrC,UAAU,MAAM,QAAQ;gBACxB,SAAS,MAAM,OAAO;gBACtB,YAAY,MAAM,WAAW;gBAC7B,gBAAgB,MAAM,eAAe;gBACrC,WAAW,MAAM,SAAS,IAAI;gBAC9B,WAAW,MAAM,UAAU;gBAC3B,YAAY,MAAM,WAAW;gBAC7B,UAAU,MAAM,SAAS;gBACzB,WAAW,MAAM,UAAU;gBAC3B,WAAW,MAAM,UAAU;gBAC3B,gBAAgB,MAAM,eAAe;gBACrC,WAAW,MAAM,UAAU;YAC7B,CAAC,MAAM,EAAE;QAET,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP,OAAO,SAAS;gBAChB;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAyB,SAAS;QAAM,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAgB,SAAS;YAAM,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,UAAU,EACV,UAAU,EACV,cAAc,EACd,QAAQ,EACR,OAAO,EACP,UAAU,EACV,SAAS,EACV,GAAG;QAEJ,2BAA2B;QAC3B,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,kBAAkB,CAAC,UAAU;YAC9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAA2B,SAAS;YAAM,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAC9C,IAAI,CAAC,uBACL,MAAM,CAAC;YACN,aAAa;YACb,aAAa;YACb,iBAAiB;YACjB;YACA;YACA,aAAa;YACb,YAAY;YACZ,YAAY,QAAQ,IAAI,CAAC,EAAE;YAC3B,iBAAiB,QAAQ,IAAI,CAAC,cAAc,IAAI;YAChD,iBAAiB;YACjB,WAAW;QACb,GACC,MAAM,GACN,MAAM;QAET,IAAI,YAAY;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAsC,SAAS;YAAM,GAC9D;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,IAAI,MAAM,EAAE;gBACZ,YAAY,MAAM,WAAW;gBAC7B,YAAY,MAAM,WAAW;gBAC7B,gBAAgB,MAAM,eAAe;gBACrC,UAAU,MAAM,QAAQ;gBACxB,SAAS,MAAM,OAAO;gBACtB,YAAY,MAAM,WAAW;gBAC7B,gBAAgB,MAAM,eAAe;gBACrC,WAAW,MAAM,SAAS,IAAI;gBAC9B,WAAW,MAAM,UAAU;gBAC3B,YAAY,MAAM,WAAW;gBAC7B,UAAU,MAAM,SAAS;gBACzB,WAAW,MAAM,UAAU;gBAC3B,WAAW,MAAM,UAAU;gBAC3B,gBAAgB,MAAM,eAAe;gBACrC,WAAW,MAAM,UAAU;YAC7B;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAyB,SAAS;QAAM,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}