{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/accessibility/SkipLink.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface SkipLinkProps {\n  href: string;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function SkipLink({ href, children, className = '' }: SkipLinkProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  return (\n    <a\n      href={href}\n      className={`\n        fixed top-4 left-4 z-50 px-4 py-2 bg-blue-600 text-white rounded-md\n        transform transition-transform duration-200 ease-in-out\n        focus:translate-y-0 focus:opacity-100\n        ${isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'}\n        ${className}\n      `}\n      onFocus={() => setIsVisible(true)}\n      onBlur={() => setIsVisible(false)}\n    >\n      {children}\n    </a>\n  );\n}\n\nexport function SkipLinks() {\n  return (\n    <>\n      <SkipLink href=\"#main-content\">Skip to main content</SkipLink>\n      <SkipLink href=\"#navigation\">Skip to navigation</SkipLink>\n      <SkipLink href=\"#search\">Skip to search</SkipLink>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAUO,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAiB;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAC;;;;QAIV,EAAE,YAAY,8BAA8B,8BAA8B;QAC1E,EAAE,UAAU;MACd,CAAC;QACD,SAAS,IAAM,aAAa;QAC5B,QAAQ,IAAM,aAAa;kBAE1B;;;;;;AAGP;AAEO,SAAS;IACd,qBACE;;0BACE,8OAAC;gBAAS,MAAK;0BAAgB;;;;;;0BAC/B,8OAAC;gBAAS,MAAK;0BAAc;;;;;;0BAC7B,8OAAC;gBAAS,MAAK;0BAAU;;;;;;;;AAG/B", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/accessibility/ScreenReaderOnly.tsx"], "sourcesContent": ["interface ScreenReaderOnlyProps {\n  children: React.ReactNode;\n  as?: keyof JSX.IntrinsicElements;\n  className?: string;\n}\n\nexport function ScreenReaderOnly({ \n  children, \n  as: Component = 'span',\n  className = '' \n}: ScreenReaderOnlyProps) {\n  return (\n    <Component \n      className={`sr-only ${className}`}\n      aria-hidden=\"false\"\n    >\n      {children}\n    </Component>\n  );\n}\n\n// Utility component for live regions\ninterface LiveRegionProps {\n  children: React.ReactNode;\n  priority?: 'polite' | 'assertive';\n  atomic?: boolean;\n  relevant?: 'additions' | 'removals' | 'text' | 'all';\n}\n\nexport function LiveRegion({ \n  children, \n  priority = 'polite',\n  atomic = true,\n  relevant = 'all'\n}: LiveRegionProps) {\n  return (\n    <div\n      aria-live={priority}\n      aria-atomic={atomic}\n      aria-relevant={relevant}\n      className=\"sr-only\"\n    >\n      {children}\n    </div>\n  );\n}\n\n// Component for status messages\ninterface StatusMessageProps {\n  message: string;\n  type?: 'success' | 'error' | 'warning' | 'info';\n  visible?: boolean;\n}\n\nexport function StatusMessage({ \n  message, \n  type = 'info',\n  visible = true \n}: StatusMessageProps) {\n  if (!visible) return null;\n\n  const priority = type === 'error' ? 'assertive' : 'polite';\n\n  return (\n    <LiveRegion priority={priority}>\n      <span role=\"status\" aria-label={`${type}: ${message}`}>\n        {message}\n      </span>\n    </LiveRegion>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAMO,SAAS,iBAAiB,EAC/B,QAAQ,EACR,IAAI,YAAY,MAAM,EACtB,YAAY,EAAE,EACQ;IACtB,qBACE,8OAAC;QACC,WAAW,CAAC,QAAQ,EAAE,WAAW;QACjC,eAAY;kBAEX;;;;;;AAGP;AAUO,SAAS,WAAW,EACzB,QAAQ,EACR,WAAW,QAAQ,EACnB,SAAS,IAAI,EACb,WAAW,KAAK,EACA;IAChB,qBACE,8OAAC;QACC,aAAW;QACX,eAAa;QACb,iBAAe;QACf,WAAU;kBAET;;;;;;AAGP;AASO,SAAS,cAAc,EAC5B,OAAO,EACP,OAAO,MAAM,EACb,UAAU,IAAI,EACK;IACnB,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,WAAW,SAAS,UAAU,cAAc;IAElD,qBACE,8OAAC;QAAW,UAAU;kBACpB,cAAA,8OAAC;YAAK,MAAK;YAAS,cAAY,GAAG,KAAK,EAAE,EAAE,SAAS;sBAClD;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 712, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useSession, signOut } from 'next-auth/react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';\nimport { SkipLinks } from '@/components/accessibility/SkipLink';\nimport { ScreenReaderOnly } from '@/components/accessibility/ScreenReaderOnly';\nimport {\n  Home,\n  FolderOpen,\n  BookOpen,\n  Users,\n  Settings,\n  LogOut,\n  Menu,\n  Bell,\n  Search,\n  Plus,\n} from 'lucide-react';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Projects', href: '/dashboard/projects', icon: FolderOpen },\n  { name: 'Terminology', href: '/dashboard/terminology', icon: BookOpen },\n  { name: 'Team', href: '/dashboard/team', icon: Users },\n  { name: 'Settings', href: '/dashboard/settings', icon: Settings },\n];\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const { data: session } = useSession();\n  const pathname = usePathname();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <SkipLinks />\n\n      {/* Mobile sidebar */}\n      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>\n        <SheetContent side=\"left\" className=\"w-64 p-0\">\n          <div className=\"flex h-full flex-col\">\n            <div className=\"flex h-16 items-center px-6 border-b\">\n              <Link href=\"/dashboard\" className=\"flex items-center\">\n                <span className=\"text-xl font-bold text-blue-600\">GlossaFlow</span>\n                <ScreenReaderOnly>- Translation Management Platform</ScreenReaderOnly>\n              </Link>\n            </div>\n            <nav\n              className=\"flex-1 space-y-1 px-3 py-4\"\n              id=\"navigation\"\n              role=\"navigation\"\n              aria-label=\"Main navigation\"\n            >\n              {navigation.map((item) => {\n                const isActive = pathname === item.href;\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-blue-100 text-blue-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                    aria-current={isActive ? 'page' : undefined}\n                  >\n                    <item.icon className=\"mr-3 h-5 w-5\" aria-hidden=\"true\" />\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n        </SheetContent>\n      </Sheet>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 items-center px-6 border-b\">\n            <Link href=\"/dashboard\" className=\"flex items-center\">\n              <span className=\"text-xl font-bold text-blue-600\">GlossaFlow</span>\n            </Link>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-3 py-4\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                    isActive\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-700 hover:bg-gray-100'\n                  }`}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top navigation */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1 items-center\">\n              <Search className=\"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 pl-3\" />\n              <input\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm\"\n                placeholder=\"Search projects, terminology...\"\n                type=\"search\"\n              />\n            </div>\n            <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n              <Button size=\"sm\" className=\"hidden sm:flex\">\n                <Plus className=\"mr-2 h-4 w-4\" />\n                New Project\n              </Button>\n\n              <Button variant=\"ghost\" size=\"sm\">\n                <Bell className=\"h-5 w-5\" />\n              </Button>\n\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarImage src={session?.user?.image || ''} alt={session?.user?.name || ''} />\n                      <AvatarFallback>\n                        {session?.user?.name?.charAt(0) || session?.user?.email?.charAt(0) || 'U'}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <DropdownMenuLabel className=\"font-normal\">\n                    <div className=\"flex flex-col space-y-1\">\n                      <p className=\"text-sm font-medium leading-none\">{session?.user?.name}</p>\n                      <p className=\"text-xs leading-none text-muted-foreground\">\n                        {session?.user?.email}\n                      </p>\n                    </div>\n                  </DropdownMenuLabel>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/profile\">Profile</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/settings\">Settings</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem\n                    className=\"text-red-600\"\n                    onClick={() => signOut({ callbackUrl: '/auth/signin' })}\n                  >\n                    <LogOut className=\"mr-2 h-4 w-4\" />\n                    Sign out\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main\n          className=\"py-6\"\n          id=\"main-content\"\n          role=\"main\"\n          aria-label=\"Main content\"\n        >\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAnBA;;;;;;;;;;;;;AAoCA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,mMAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,kNAAA,CAAA,aAAU;IAAC;IAClE;QAAE,MAAM;QAAe,MAAM;QAA0B,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACtE;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACrD;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACjE;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+IAAA,CAAA,YAAS;;;;;0BAGV,8OAAC,iIAAA,CAAA,QAAK;gBAAC,MAAM;gBAAa,cAAc;0BACtC,cAAA,8OAAC,iIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAO,WAAU;8BAClC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;;sDAChC,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;sDAClD,8OAAC,uJAAA,CAAA,mBAAgB;sDAAC;;;;;;;;;;;;;;;;;0CAGtB,8OAAC;gCACC,WAAU;gCACV,IAAG;gCACH,MAAK;gCACL,cAAW;0CAEV,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,mCACJ;wCACF,SAAS,IAAM,eAAe;wCAC9B,gBAAc,WAAW,SAAS;;0DAElC,8OAAC,KAAK,IAAI;gDAAC,WAAU;gDAAe,eAAY;;;;;;4CAC/C,KAAK,IAAI;;uCAXL,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;;;;;;;;;;;;0BAOR,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAChC,cAAA,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;sCAGtD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,mCACJ;;sDAEF,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCATL,KAAK,IAAI;;;;;4BAYpB;;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDACC,WAAU;gDACV,aAAY;gDACZ,MAAK;;;;;;;;;;;;kDAGT,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;;kEAC1B,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAInC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAC3B,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAGlB,8OAAC,4IAAA,CAAA,eAAY;;kEACX,8OAAC,4IAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,WAAU;sEAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,8OAAC,kIAAA,CAAA,cAAW;wEAAC,KAAK,SAAS,MAAM,SAAS;wEAAI,KAAK,SAAS,MAAM,QAAQ;;;;;;kFAC1E,8OAAC,kIAAA,CAAA,iBAAc;kFACZ,SAAS,MAAM,MAAM,OAAO,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;kEAK9E,8OAAC,4IAAA,CAAA,sBAAmB;wDAAC,WAAU;wDAAO,OAAM;wDAAM,UAAU;;0EAC1D,8OAAC,4IAAA,CAAA,oBAAiB;gEAAC,WAAU;0EAC3B,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAoC,SAAS,MAAM;;;;;;sFAChE,8OAAC;4EAAE,WAAU;sFACV,SAAS,MAAM;;;;;;;;;;;;;;;;;0EAItB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0EACtB,8OAAC,4IAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;8EAAqB;;;;;;;;;;;0EAElC,8OAAC,4IAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;8EAAsB;;;;;;;;;;;0EAEnC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0EACtB,8OAAC,4IAAA,CAAA,mBAAgB;gEACf,WAAU;gEACV,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;wEAAE,aAAa;oEAAe;;kFAErD,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/C,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,MAAK;wBACL,cAAW;kCAEX,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1381, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 1420, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\nimport { DashboardLayout } from '@/components/layout/DashboardLayout';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport {\n  Loader2,\n  FolderOpen,\n  BookOpen,\n  Users,\n  TrendingUp,\n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Plus,\n  ArrowRight,\n  RefreshCw,\n  UserPlus\n} from 'lucide-react';\nimport {\n  useGetDashboardStatsQuery,\n  useGetDashboardActivityQuery,\n  useGetRecentProjectsQuery,\n  useRefreshDashboardMutation\n} from '@/lib/api/dashboard';\nimport { InviteTeamMemberDialog } from '@/components/team/InviteTeamMemberDialog';\n\n// Mock data for demonstration\nconst mockStats = {\n  totalProjects: 12,\n  activeProjects: 8,\n  completedProjects: 4,\n  totalTerms: 1247,\n  teamMembers: 6,\n  pendingReviews: 23,\n};\n\nconst mockRecentProjects = [\n  {\n    id: '1',\n    name: 'Mobile App Localization',\n    status: 'in_progress',\n    progress: 75,\n    dueDate: '2024-02-15',\n    language: 'Japanese',\n  },\n  {\n    id: '2',\n    name: 'Website Translation',\n    status: 'review',\n    progress: 90,\n    dueDate: '2024-02-10',\n    language: 'Spanish',\n  },\n  {\n    id: '3',\n    name: 'Documentation Update',\n    status: 'completed',\n    progress: 100,\n    dueDate: '2024-02-05',\n    language: 'French',\n  },\n];\n\nexport default function DashboardPage() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (status === 'unauthenticated') {\n      router.push('/auth/signin');\n    }\n  }, [status, router]);\n\n  if (status === 'loading') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-4\" />\n          <p className=\"text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (status === 'unauthenticated') {\n    return null; // Will redirect\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Welcome Header */}\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            Welcome back, {session?.user?.name || 'User'}!\n          </h1>\n          <p className=\"text-gray-600\">\n            Here's what's happening with your translation projects today.\n          </p>\n        </div>\n\n        {/* Stats Overview */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <FolderOpen className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Total Projects</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{mockStats.totalProjects}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-green-100 rounded-lg\">\n                  <TrendingUp className=\"h-6 w-6 text-green-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Active Projects</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{mockStats.activeProjects}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-purple-100 rounded-lg\">\n                  <BookOpen className=\"h-6 w-6 text-purple-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Terminology</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{mockStats.totalTerms}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-orange-100 rounded-lg\">\n                  <Users className=\"h-6 w-6 text-orange-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Team Members</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{mockStats.teamMembers}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Recent Projects */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between\">\n              <div>\n                <CardTitle>Recent Projects</CardTitle>\n                <CardDescription>Your latest translation projects</CardDescription>\n              </div>\n              <Button size=\"sm\">\n                <Plus className=\"mr-2 h-4 w-4\" />\n                New Project\n              </Button>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {mockRecentProjects.map((project) => (\n                  <div key={project.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <h4 className=\"font-medium\">{project.name}</h4>\n                        <Badge\n                          variant={\n                            project.status === 'completed' ? 'default' :\n                            project.status === 'review' ? 'secondary' : 'outline'\n                          }\n                        >\n                          {project.status === 'in_progress' ? 'In Progress' :\n                           project.status === 'review' ? 'In Review' : 'Completed'}\n                        </Badge>\n                      </div>\n                      <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                        <span>Target: {project.language}</span>\n                        <span className=\"mx-2\">•</span>\n                        <span>Due: {project.dueDate}</span>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Progress value={project.progress} className=\"flex-1 mr-2\" />\n                        <span className=\"text-sm text-gray-600\">{project.progress}%</span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n              <div className=\"mt-4\">\n                <Button variant=\"ghost\" className=\"w-full\">\n                  View All Projects\n                  <ArrowRight className=\"ml-2 h-4 w-4\" />\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Activity Feed */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Recent Activity</CardTitle>\n              <CardDescription>Latest updates from your team</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"p-1 bg-green-100 rounded-full\">\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm\">\n                      <span className=\"font-medium\">Sarah Chen</span> completed translation for\n                      <span className=\"font-medium\"> Mobile App Localization</span>\n                    </p>\n                    <p className=\"text-xs text-gray-500\">2 hours ago</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"p-1 bg-blue-100 rounded-full\">\n                    <Clock className=\"h-4 w-4 text-blue-600\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm\">\n                      <span className=\"font-medium\">Mike Johnson</span> started review for\n                      <span className=\"font-medium\"> Website Translation</span>\n                    </p>\n                    <p className=\"text-xs text-gray-500\">4 hours ago</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"p-1 bg-orange-100 rounded-full\">\n                    <AlertCircle className=\"h-4 w-4 text-orange-600\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm\">\n                      <span className=\"font-medium\">Documentation Update</span> deadline approaching\n                    </p>\n                    <p className=\"text-xs text-gray-500\">6 hours ago</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"p-1 bg-purple-100 rounded-full\">\n                    <BookOpen className=\"h-4 w-4 text-purple-600\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm\">\n                      <span className=\"font-medium\">Emma Davis</span> added 15 new terms to\n                      <span className=\"font-medium\"> Technical Glossary</span>\n                    </p>\n                    <p className=\"text-xs text-gray-500\">1 day ago</p>\n                  </div>\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <Button variant=\"ghost\" className=\"w-full\">\n                  View All Activity\n                  <ArrowRight className=\"ml-2 h-4 w-4\" />\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Quick Actions */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Quick Actions</CardTitle>\n            <CardDescription>Get started with common tasks</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <Button className=\"h-20 flex-col\">\n                <Plus className=\"h-6 w-6 mb-2\" />\n                Create Project\n              </Button>\n              <Button variant=\"outline\" className=\"h-20 flex-col\">\n                <BookOpen className=\"h-6 w-6 mb-2\" />\n                Manage Terminology\n              </Button>\n              <Button variant=\"outline\" className=\"h-20 flex-col\">\n                <Users className=\"h-6 w-6 mb-2\" />\n                Invite Team Member\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;;;;;;;;;;;AAkCA,8BAA8B;AAC9B,MAAM,YAAY;IAChB,eAAe;IACf,gBAAgB;IAChB,mBAAmB;IACnB,YAAY;IACZ,aAAa;IACb,gBAAgB;AAClB;AAEA,MAAM,qBAAqB;IACzB;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,SAAS;QACT,UAAU;IACZ;CACD;AAEc,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,mBAAmB;YAChC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAQ;KAAO;IAEnB,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,WAAW,mBAAmB;QAChC,OAAO,MAAM,gBAAgB;IAC/B;IAEA,qBACE,8OAAC,+IAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;;gCAAmC;gCAChC,SAAS,MAAM,QAAQ;gCAAO;;;;;;;sCAE/C,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAM/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAoC,UAAU,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhF,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAoC,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMjF,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAoC,UAAU,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM7E,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAoC,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC;;8DACC,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAEnB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;;8DACX,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAIrC,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,mBAAmB,GAAG,CAAC,CAAC,wBACvB,8OAAC;oDAAqB,WAAU;8DAC9B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAe,QAAQ,IAAI;;;;;;kFACzC,8OAAC,iIAAA,CAAA,QAAK;wEACJ,SACE,QAAQ,MAAM,KAAK,cAAc,YACjC,QAAQ,MAAM,KAAK,WAAW,cAAc;kFAG7C,QAAQ,MAAM,KAAK,gBAAgB,gBACnC,QAAQ,MAAM,KAAK,WAAW,cAAc;;;;;;;;;;;;0EAGjD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;4EAAK;4EAAS,QAAQ,QAAQ;;;;;;;kFAC/B,8OAAC;wEAAK,WAAU;kFAAO;;;;;;kFACvB,8OAAC;;4EAAK;4EAAM,QAAQ,OAAO;;;;;;;;;;;;;0EAE7B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oIAAA,CAAA,WAAQ;wEAAC,OAAO,QAAQ,QAAQ;wEAAE,WAAU;;;;;;kFAC7C,8OAAC;wEAAK,WAAU;;4EAAyB,QAAQ,QAAQ;4EAAC;;;;;;;;;;;;;;;;;;;mDArBtD,QAAQ,EAAE;;;;;;;;;;sDA2BxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;;oDAAS;kEAEzC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO9B,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;sEAEzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;;sFACX,8OAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAiB;sFAC/C,8OAAC;4EAAK,WAAU;sFAAc;;;;;;;;;;;;8EAEhC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;;sFACX,8OAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAmB;sFACjD,8OAAC;4EAAK,WAAU;sFAAc;;;;;;;;;;;;8EAEhC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;sEAEzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;;sFACX,8OAAC;4EAAK,WAAU;sFAAc;;;;;;wEAA2B;;;;;;;8EAE3D,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;;sFACX,8OAAC;4EAAK,WAAU;sFAAc;;;;;;wEAAiB;sFAC/C,8OAAC;4EAAK,WAAU;sFAAc;;;;;;;;;;;;8EAEhC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAI3C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;;oDAAS;kEAEzC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhC,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD", "debugId": null}}]}