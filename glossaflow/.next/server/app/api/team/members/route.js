const CHUNK_PUBLIC_PATH = "server/app/api/team/members/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_node-fetch_lib_index_5a5b81d8.js");
runtime.loadChunk("server/chunks/node_modules_next_2bcf283f._.js");
runtime.loadChunk("server/chunks/node_modules_next-auth_2070941b._.js");
runtime.loadChunk("server/chunks/node_modules_openid-client_ef38b3be._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_cjs_b4a80197._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_ws_daabdc74._.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_auth-js_dist_module_4e4e8dc6._.js");
runtime.loadChunk("server/chunks/node_modules_aa03fb34._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__8aef92e2._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/team/members/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/team/members/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/team/members/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
