import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getSession } from 'next-auth/react';

const baseQuery = fetchBaseQuery({
  baseUrl: '/api',
  prepareHeaders: async (headers) => {
    const session = await getSession();
    if (session?.accessToken) {
      headers.set('authorization', `Bearer ${session.accessToken}`);
    }
    return headers;
  },
});

export const baseApi = createApi({
  reducerPath: 'api',
  baseQuery,
  tagTypes: [
    'User',
    'Project',
    'Terminology',
    'Translation',
    'Comment',
    'File',
    'Team',
    'TeamMember',
    'TeamRole',
    'TeamInvitation',
    'Organization',
  ],
  endpoints: () => ({}),
});

export type ApiResponse<T> = {
  data: T;
  message?: string;
  success: boolean;
};

export type PaginatedResponse<T> = ApiResponse<{
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}>;

export type ApiError = {
  error: string;
  message?: string;
  statusCode?: number;
};
